# 电影视频上传和播放功能使用指南

## 🎬 功能概述

本系统实现了完整的电影视频上传和播放功能，只有登录管理后台的用户才能观看上传的影片。

## 📋 功能特性

### 管理端功能
- ✅ 视频文件上传（支持MP4、MOV、AVI、WebM、WMV、FLV格式）
- ✅ 文件大小限制（最大500MB）
- ✅ 上传进度显示
- ✅ 文件格式验证和安全检查
- ✅ 自动文件命名和存储管理

### 播放功能
- ✅ 认证保护（只有管理员可观看）
- ✅ 全屏播放支持
- ✅ 完整的播放控制（播放/暂停、进度条、音量控制）
- ✅ 视频流传输（支持大文件播放）
- ✅ 响应式设计

## 🚀 使用方法

### 1. 上传视频文件

1. 登录管理后台：访问 `/admin` 页面
2. 切换到"电影管理"标签
3. 点击"添加新电影"按钮
4. 填写电影基本信息（标题、描述、分类等）
5. 在"视频文件上传"部分：
   - 点击"选择文件"按钮
   - 选择视频文件（支持的格式：MP4、MOV、AVI、WebM、WMV、FLV）
   - 等待上传完成（会显示进度条）
   - 上传成功后会显示绿色确认信息
6. 点击"保存"按钮完成添加

### 2. 观看视频

1. 访问 `/movie` 页面查看电影列表
2. 对于已上传视频的电影，会显示"观看影片"按钮
3. 如果未登录管理后台，按钮会显示"需要登录"
4. 登录后点击"观看影片"按钮即可播放视频
5. 视频在全屏模式下播放，支持：
   - 点击播放/暂停
   - 拖拽进度条跳转
   - 音量控制
   - 全屏切换
   - 鼠标移动显示控制栏

## 🔧 技术实现

### 文件存储
- 上传的视频文件存储在 `public/uploads/movies/` 目录
- 文件名格式：`movie_[时间戳].[扩展名]`
- 数据库中存储相对路径：`/uploads/movies/filename.ext`

### 安全机制
- 文件类型验证：只允许指定的视频格式
- 文件大小限制：最大500MB
- 认证保护：只有管理员可以观看视频
- 路径安全：防止目录遍历攻击

### API端点
- `POST /api/upload/video` - 视频文件上传
- `GET /api/video/[filename]` - 认证视频流
- `PUT /api/admin/movies` - 更新电影信息（包含视频路径）

## 📱 用户界面

### 电影卡片状态
- **有视频 + 已登录**：显示蓝色"观看影片"按钮
- **有视频 + 未登录**：显示灰色"需要登录"按钮
- **无视频**：显示灰色"暂无资源"按钮
- **外部链接**：显示蓝色外部链接图标按钮

### 视频播放器
- 全屏黑色背景
- 顶部显示电影标题
- 右上角关闭按钮
- 底部控制栏（鼠标移动时显示）
- 支持键盘和鼠标操作

## 🛠️ 部署注意事项

### 文件权限
确保 `public/uploads/movies/` 目录有写入权限：
```bash
mkdir -p public/uploads/movies
chmod 755 public/uploads/movies
```

### 环境变量
无需额外环境变量，使用现有的管理员认证系统。

### 服务器配置
- 确保服务器支持大文件上传
- 配置适当的超时时间
- 考虑使用CDN加速视频传输

## 🔍 故障排除

### 上传失败
1. 检查文件格式是否支持
2. 确认文件大小不超过500MB
3. 检查网络连接
4. 确认已登录管理后台

### 播放失败
1. 确认已登录管理后台
2. 检查视频文件是否存在
3. 尝试刷新页面
4. 检查浏览器控制台错误信息

### 权限问题
1. 确认使用正确的管理员账号
2. 检查localStorage中的认证信息
3. 重新登录管理后台

## 📈 未来扩展

可以考虑的功能扩展：
- 视频转码和压缩
- 多分辨率支持
- 字幕支持
- 播放历史记录
- 视频预览缩略图
- 批量上传功能
