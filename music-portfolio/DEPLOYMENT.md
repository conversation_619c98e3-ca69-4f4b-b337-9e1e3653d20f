# 部署指南

## 本地开发

1. **安装依赖**
   ```bash
   npm install
   ```

2. **配置环境变量**
   ```bash
   cp .env.local.example .env.local
   ```
   编辑 `.env.local` 文件，设置管理员账号密码。

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **访问网站**
   - 前台: http://localhost:3000
   - 后台管理: http://localhost:3000/admin

## 生产部署

### Vercel 部署 (推荐)

1. **推送代码到 GitHub**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin your-github-repo-url
   git push -u origin main
   ```

2. **在 Vercel 中导入项目**
   - 访问 https://vercel.com
   - 点击 "New Project"
   - 导入您的 GitHub 仓库

3. **配置环境变量**
   在 Vercel 项目设置中添加环境变量：
   - `ADMIN_USERNAME`: 管理员用户名
   - `ADMIN_PASSWORD`: 管理员密码

4. **部署**
   Vercel 会自动构建和部署您的项目。

### 其他平台部署

项目支持部署到任何支持 Node.js 的平台：

- **Netlify**: 支持 Next.js 部署
- **Railway**: 简单的 Node.js 部署
- **DigitalOcean App Platform**: 容器化部署
- **自建服务器**: 使用 PM2 或 Docker

## 自定义配置

### 1. 替换头像图片
将您的头像图片命名为 `avatar.jpg` 并放置在 `public/` 目录下。

### 2. 添加媒体文件
创建以下目录结构：
```
public/
├── posters/          # 电影海报图片
├── thumbnails/       # 视频缩略图
└── music/           # 音乐文件 (如果需要)
```

### 3. 配置内容
- **音乐视频**: 在后台管理系统中添加B站视频链接
- **电影信息**: 在后台管理系统中添加电影详情和海报
- **分类管理**: 支持自定义电影分类

### 4. 数据库集成 (可选)
如需使用真实数据库而非模拟数据：

1. 注册 Supabase 账号
2. 创建新项目
3. 在 `.env.local` 中配置 Supabase 连接信息
4. 创建数据表（参考 `src/lib/supabase.ts` 中的类型定义）

## 维护

### 更新内容
- **音乐管理**: 通过后台管理系统添加/编辑/删除B站音乐视频
- **电影管理**: 通过后台管理系统管理电影信息和海报
- **文件上传**: 支持海报和缩略图上传
- **分类管理**: 可以自定义电影分类

### 备份
- 定期备份 `.env.local` 文件
- 如使用数据库，定期备份数据库

### 监控
- 使用 Vercel Analytics 监控网站访问情况
- 检查服务器日志排查问题

## 故障排除

### 常见问题

1. **字体加载失败**
   - 已使用系统字体替代，无需额外配置

2. **图片无法显示**
   - 检查图片文件是否存在于 `public/` 目录
   - 确认图片文件名和路径正确

3. **B站视频无法播放**
   - 检查B站视频链接是否正确
   - 确认视频是否公开可访问
   - 支持的链接格式：
     - `https://www.bilibili.com/video/BV...`
     - `https://www.bilibili.com/video/av...`
     - `https://b23.tv/...`

4. **文件上传失败**
   - 检查文件大小是否超过5MB
   - 确认文件格式为JPEG、PNG或WebP
   - 检查服务器写入权限

### 获取帮助
如遇到问题，请检查：
1. 浏览器控制台错误信息
2. 服务器日志
3. 网络连接状态
