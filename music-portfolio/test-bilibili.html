<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B站缩略图获取测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        button {
            background-color: #00a1d6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0085b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .thumbnail {
            max-width: 300px;
            margin-top: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>B站缩略图获取测试</h1>
        <p>输入B站视频链接来测试自动获取缩略图功能：</p>
        
        <input type="text" id="urlInput" placeholder="https://www.bilibili.com/video/BV..." />
        <button onclick="fetchThumbnail()" id="fetchBtn">获取缩略图</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function fetchThumbnail() {
            const url = document.getElementById('urlInput').value;
            const btn = document.getElementById('fetchBtn');
            const result = document.getElementById('result');
            
            if (!url) {
                result.innerHTML = '<div class="error">请输入B站视频链接</div>';
                return;
            }
            
            btn.disabled = true;
            btn.textContent = '获取中...';
            result.innerHTML = '';
            
            try {
                const response = await fetch(`/api/bilibili?url=${encodeURIComponent(url)}`);
                const data = await response.json();
                
                if (data.success) {
                    result.innerHTML = `
                        <div class="success">
                            <h3>获取成功！</h3>
                            <p><strong>视频标题：</strong>${data.data.title}</p>
                            <p><strong>视频ID：</strong>${data.data.bvid}</p>
                            <p><strong>缩略图URL：</strong><br><a href="${data.data.thumbnail}" target="_blank">${data.data.thumbnail}</a></p>
                            ${data.data.thumbnail ? `<img src="${data.data.thumbnail}" alt="缩略图" class="thumbnail" />` : ''}
                        </div>
                    `;
                } else {
                    result.innerHTML = `<div class="error">获取失败：${data.error}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">请求失败：${error.message}</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = '获取缩略图';
            }
        }
        
        // 回车键触发
        document.getElementById('urlInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                fetchThumbnail();
            }
        });
    </script>
</body>
</html>
