{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/movie/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Film, Star, ExternalLink, Calendar } from 'lucide-react'\nimport Image from 'next/image'\n\ninterface Movie {\n  id: string\n  title: string\n  description: string\n  poster_url?: string\n  movie_link?: string\n  category: string\n  rating?: number\n  release_year?: number\n  created_at: string\n}\n\nexport default function MoviePage() {\n  const [movies, setMovies] = useState<Movie[]>([])\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n  const [isLoading, setIsLoading] = useState(true)\n\n  const categories = [\n    { id: 'all', label: '全部' },\n    { id: 'gangster', label: '黑帮类' },\n    { id: 'romance', label: '情感类' },\n    { id: 'history', label: '历史类' },\n    { id: 'thriller', label: '惊悚类' },\n    { id: 'drama', label: '剧情类' },\n    { id: 'action', label: '动作类' },\n  ]\n\n  useEffect(() => {\n    fetchMovies()\n  }, [])\n\n  const fetchMovies = async () => {\n    try {\n      const response = await fetch('/api/movies')\n      if (response.ok) {\n        const data = await response.json()\n        setMovies(data.data)\n      }\n    } catch (error) {\n      console.error('Failed to fetch movies:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const filteredMovies = selectedCategory === 'all' \n    ? movies \n    : movies.filter(movie => movie.category === selectedCategory)\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen py-12 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-red-400 mx-auto mb-4\"></div>\n          <p className=\"text-gray-400\">Loading movies...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen py-12\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"flex justify-center mb-6\">\n            <Film className=\"text-red-400\" size={48} />\n          </div>\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6 tracking-wider\">MOVIES</h1>\n          <p className=\"text-xl text-gray-400 max-w-2xl mx-auto\">\n            Discover the films that inspire me - from classic gangster movies to emotional dramas and historical epics\n          </p>\n        </div>\n\n        {/* Category Filter */}\n        <div className=\"mb-12\">\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {categories.map((category) => (\n              <button\n                key={category.id}\n                onClick={() => setSelectedCategory(category.id)}\n                className={`px-6 py-3 rounded-full font-semibold transition-all duration-200 ${\n                  selectedCategory === category.id\n                    ? 'bg-red-600 text-white shadow-lg'\n                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white'\n                }`}\n              >\n                {category.label}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        {/* Movies Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\n          {filteredMovies.map((movie) => (\n            <div\n              key={movie.id}\n              className=\"bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-all duration-300 hover:scale-105 hover:shadow-xl\"\n            >\n              {/* Movie Poster */}\n              <div className=\"relative aspect-[2/3] bg-gray-600\">\n                {movie.poster_url ? (\n                  <Image\n                    src={movie.poster_url}\n                    alt={movie.title}\n                    fill\n                    className=\"object-cover\"\n                  />\n                ) : (\n                  <div className=\"flex items-center justify-center h-full\">\n                    <Film className=\"text-gray-400\" size={64} />\n                  </div>\n                )}\n                \n                {/* Rating Badge */}\n                {movie.rating && (\n                  <div className=\"absolute top-4 right-4 bg-black/70 backdrop-blur-sm rounded-full px-3 py-1 flex items-center\">\n                    <Star className=\"text-yellow-400 mr-1\" size={16} fill=\"currentColor\" />\n                    <span className=\"text-white text-sm font-semibold\">{movie.rating}</span>\n                  </div>\n                )}\n\n                {/* External Link Button */}\n                {movie.movie_link && (\n                  <div className=\"absolute bottom-4 right-4\">\n                    <a\n                      href={movie.movie_link}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"w-10 h-10 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center transition-colors duration-200\"\n                      title=\"观看电影\"\n                    >\n                      <ExternalLink className=\"text-white\" size={20} />\n                    </a>\n                  </div>\n                )}\n              </div>\n\n              {/* Movie Info */}\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-semibold mb-2 line-clamp-2\">{movie.title}</h3>\n                \n                {/* Category and Year */}\n                <div className=\"flex items-center justify-between mb-3\">\n                  <span className=\"px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded-full\">\n                    {categories.find(cat => cat.id === movie.category)?.label || movie.category}\n                  </span>\n                  {movie.release_year && (\n                    <div className=\"flex items-center text-gray-400 text-sm\">\n                      <Calendar className=\"mr-1\" size={14} />\n                      <span>{movie.release_year}</span>\n                    </div>\n                  )}\n                </div>\n\n                {/* Description */}\n                <p className=\"text-gray-400 text-sm line-clamp-3 mb-4\">\n                  {movie.description}\n                </p>\n\n                {/* Action Button */}\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-xs text-gray-500\">\n                    添加于 {new Date(movie.created_at).toLocaleDateString('zh-CN')}\n                  </span>\n                  {movie.movie_link && (\n                    <a\n                      href={movie.movie_link}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-red-400 hover:text-red-300 text-sm font-semibold transition-colors duration-200\"\n                    >\n                      观看 →\n                    </a>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Empty State */}\n        {filteredMovies.length === 0 && (\n          <div className=\"text-center py-20\">\n            <Film className=\"mx-auto text-gray-600 mb-4\" size={64} />\n            <h3 className=\"text-2xl font-semibold text-gray-400 mb-2\">\n              {selectedCategory === 'all' ? 'No Movies Available' : 'No Movies in This Category'}\n            </h3>\n            <p className=\"text-gray-500\">\n              {selectedCategory === 'all' \n                ? 'Stay tuned for movie recommendations' \n                : 'Try selecting a different category'}\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAkBe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,OAAO;QAAK;QACzB;YAAE,IAAI;YAAY,OAAO;QAAM;QAC/B;YAAE,IAAI;YAAW,OAAO;QAAM;QAC9B;YAAE,IAAI;YAAW,OAAO;QAAM;QAC9B;YAAE,IAAI;YAAY,OAAO;QAAM;QAC/B;YAAE,IAAI;YAAS,OAAO;QAAM;QAC5B;YAAE,IAAI;YAAU,OAAO;QAAM;KAC9B;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,UAAU,KAAK,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,qBAAqB,QACxC,SACA,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;IAE9C,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;gCAAe,MAAM;;;;;;;;;;;sCAEvC,6LAAC;4BAAG,WAAU;sCAAqD;;;;;;sCACnE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;gCAEC,SAAS,IAAM,oBAAoB,SAAS,EAAE;gCAC9C,WAAW,AAAC,oEAIX,OAHC,qBAAqB,SAAS,EAAE,GAC5B,oCACA;0CAGL,SAAS,KAAK;+BARV,SAAS,EAAE;;;;;;;;;;;;;;;8BAexB,6LAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC;4BAmDV;6CAlDT,6LAAC;4BAEC,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;wCACZ,MAAM,UAAU,iBACf,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,MAAM,UAAU;4CACrB,KAAK,MAAM,KAAK;4CAChB,IAAI;4CACJ,WAAU;;;;;iEAGZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;gDAAgB,MAAM;;;;;;;;;;;wCAKzC,MAAM,MAAM,kBACX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;oDAAuB,MAAM;oDAAI,MAAK;;;;;;8DACtD,6LAAC;oDAAK,WAAU;8DAAoC,MAAM,MAAM;;;;;;;;;;;;wCAKnE,MAAM,UAAU,kBACf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,MAAM,MAAM,UAAU;gDACtB,QAAO;gDACP,KAAI;gDACJ,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;oDAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;8CAOnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C,MAAM,KAAK;;;;;;sDAGpE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACb,EAAA,mBAAA,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,MAAM,QAAQ,eAAhD,uCAAA,iBAAmD,KAAK,KAAI,MAAM,QAAQ;;;;;;gDAE5E,MAAM,YAAY,kBACjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;4DAAO,MAAM;;;;;;sEACjC,6LAAC;sEAAM,MAAM,YAAY;;;;;;;;;;;;;;;;;;sDAM/B,6LAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;sDAIpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;wDAAwB;wDACjC,IAAI,KAAK,MAAM,UAAU,EAAE,kBAAkB,CAAC;;;;;;;gDAEpD,MAAM,UAAU,kBACf,6LAAC;oDACC,MAAM,MAAM,UAAU;oDACtB,QAAO;oDACP,KAAI;oDACJ,WAAU;8DACX;;;;;;;;;;;;;;;;;;;2BA3EF,MAAM,EAAE;;;;;;;;;;;gBAsFlB,eAAe,MAAM,KAAK,mBACzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;4BAA6B,MAAM;;;;;;sCACnD,6LAAC;4BAAG,WAAU;sCACX,qBAAqB,QAAQ,wBAAwB;;;;;;sCAExD,6LAAC;4BAAE,WAAU;sCACV,qBAAqB,QAClB,yCACA;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GA3LwB;KAAA", "debugId": null}}]}