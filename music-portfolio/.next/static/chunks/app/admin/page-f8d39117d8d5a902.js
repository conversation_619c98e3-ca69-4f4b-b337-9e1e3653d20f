(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{227:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(9946).A)("music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]])},1066:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(9946).A)("film",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M7 3v18",key:"bbkbws"}],["path",{d:"M3 7.5h4",key:"zfgn84"}],["path",{d:"M3 12h18",key:"1i2n21"}],["path",{d:"M3 16.5h4",key:"1230mu"}],["path",{d:"M17 3v18",key:"in4fa5"}],["path",{d:"M17 7.5h4",key:"myr1c1"}],["path",{d:"M17 16.5h4",key:"go4c1d"}]])},2108:(e,t,a)=>{Promise.resolve().then(a.bind(a,2892))},2892:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var l=a(5155),r=a(2115),s=a(9946);let i=(0,s.A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var n=a(227),o=a(1066);let c=(0,s.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),d=(0,s.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var m=a(9803);let u=(0,s.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),x=(0,s.A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]]);function b(){let[e,t]=(0,r.useState)("music"),[a,s]=(0,r.useState)(!1),[d,m]=(0,r.useState)(!0);(0,r.useEffect)(()=>{u()},[]);let u=async()=>{try{let e=localStorage.getItem("admin_username"),t=localStorage.getItem("admin_password");if(e&&t){let a=btoa("".concat(e,":").concat(t)),l=await fetch("/api/admin/auth",{headers:{Authorization:"Basic ".concat(a)}});s(l.ok),l.ok||(localStorage.removeItem("admin_username"),localStorage.removeItem("admin_password"))}else s(!1)}catch(e){s(!1),localStorage.removeItem("admin_username"),localStorage.removeItem("admin_password")}finally{m(!1)}};if(d)return(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-gray-400",children:"加载中..."})]})});if(!a)return(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsxs)("div",{className:"bg-gray-800 p-8 rounded-lg max-w-md w-full mx-4",children:[(0,l.jsxs)("div",{className:"text-center mb-8",children:[(0,l.jsx)(i,{className:"mx-auto text-blue-400 mb-4",size:48}),(0,l.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"后台管理系统"}),(0,l.jsx)("p",{className:"text-gray-400",children:"请登录以访问管理功能"})]}),(0,l.jsx)("button",{onClick:()=>{let e=prompt("请输入用户名:"),t=prompt("请输入密码:");if(e&&t){let a=btoa("".concat(e,":").concat(t));fetch("/api/admin/auth",{method:"POST",headers:{Authorization:"Basic ".concat(a)}}).then(a=>{a.ok?(localStorage.setItem("admin_username",e),localStorage.setItem("admin_password",t),s(!0)):alert("认证失败，请检查用户名和密码")}).catch(e=>{console.error("Login error:",e),alert("登录失败，请重试")})}},className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200",children:"登录"})]})});let x=[{id:"music",label:"音乐管理",icon:n.A},{id:"movie",label:"电影管理",icon:o.A},{id:"profile",label:"个人信息",icon:c}];return(0,l.jsx)("div",{className:"min-h-screen",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsxs)("div",{className:"mb-8 flex justify-between items-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"后台管理系统"}),(0,l.jsx)("p",{className:"text-gray-400",children:"管理您的音乐和电影内容"})]}),(0,l.jsx)("button",{onClick:()=>{confirm("确定要退出登录吗？")&&(localStorage.removeItem("admin_username"),localStorage.removeItem("admin_password"),s(!1))},className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200",children:"退出登录"})]}),(0,l.jsx)("div",{className:"border-b border-gray-700 mb-8",children:(0,l.jsx)("nav",{className:"flex space-x-8",children:x.map(a=>{let r=a.icon;return(0,l.jsxs)("button",{onClick:()=>t(a.id),className:"flex items-center py-4 px-1 border-b-2 font-medium text-sm ".concat(e===a.id?"border-blue-500 text-blue-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"),children:[(0,l.jsx)(r,{className:"mr-2",size:20}),a.label]},a.id)})})}),(0,l.jsxs)("div",{className:"bg-gray-800 rounded-lg p-6",children:["music"===e&&(0,l.jsx)(h,{}),"movie"===e&&(0,l.jsx)(p,{}),"profile"===e&&(0,l.jsx)(g,{})]})]})})}function h(){let[e,t]=(0,r.useState)([]),[a,s]=(0,r.useState)(!0),[i,n]=(0,r.useState)(!1),[o,c]=(0,r.useState)(null),[b,h]=(0,r.useState)(!1),[p,g]=(0,r.useState)({title:"",artist:"",description:"",bilibili_url:"",thumbnail_url:""});(0,r.useEffect)(()=>{y()},[]);let y=async()=>{try{let e=btoa("".concat(localStorage.getItem("admin_username"),":").concat(localStorage.getItem("admin_password"))),a=await fetch("/api/admin/music",{headers:{Authorization:"Basic ".concat(e)}});if(a.ok){let e=await a.json();t(e.data)}}catch(e){console.error("Failed to fetch music videos:",e)}finally{s(!1)}},f=async e=>{e.preventDefault();try{let e=btoa("".concat(localStorage.getItem("admin_username"),":").concat(localStorage.getItem("admin_password"))),t=o?"PUT":"POST",a=o?{...p,id:o.id}:p,l=await fetch("/api/admin/music",{method:t,headers:{"Content-Type":"application/json",Authorization:"Basic ".concat(e)},body:JSON.stringify(a)});if(l.ok)n(!1),c(null),g({title:"",artist:"",description:"",bilibili_url:"",thumbnail_url:""}),y();else{let e=await l.json();alert(e.error||"操作失败，请检查输入信息")}}catch(e){console.error("Failed to submit:",e),alert("操作失败")}},v=async e=>{if(confirm("确定要删除这个音乐视频吗？"))try{let t=btoa("".concat(localStorage.getItem("admin_username"),":").concat(localStorage.getItem("admin_password")));(await fetch("/api/admin/music?id=".concat(e),{method:"DELETE",headers:{Authorization:"Basic ".concat(t)}})).ok?y():alert("删除失败")}catch(e){console.error("Failed to delete video:",e),alert("删除失败")}},j=async()=>{if(!p.bilibili_url)return void alert("请先输入B站视频链接");h(!0);try{let e=await fetch("/api/bilibili?url=".concat(encodeURIComponent(p.bilibili_url))),t=await e.json();t.success&&t.data.thumbnail?(g(e=>({...e,thumbnail_url:t.data.thumbnail,title:e.title||t.data.title||e.title})),alert("缩略图获取成功！")):alert("获取缩略图失败："+(t.error||"未知错误"))}catch(e){console.error("Error fetching thumbnail:",e),alert("获取缩略图失败，请检查网络连接")}finally{h(!1)}};return a?(0,l.jsx)("div",{className:"text-center py-8",children:"加载中..."}):(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,l.jsx)("h2",{className:"text-2xl font-semibold",children:"音乐视频管理"}),(0,l.jsxs)("button",{onClick:()=>n(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center",children:[(0,l.jsx)(d,{className:"mr-2",size:20}),"添加音乐视频"]})]}),i&&(0,l.jsxs)("div",{className:"bg-gray-700 p-6 rounded-lg mb-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:o?"编辑音乐视频":"添加新音乐视频"}),(0,l.jsxs)("form",{onSubmit:f,className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"视频标题 *"}),(0,l.jsx)("input",{type:"text",required:!0,value:p.title,onChange:e=>g({...p,title:e.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"演唱者 *"}),(0,l.jsx)("input",{type:"text",required:!0,value:p.artist,onChange:e=>g({...p,artist:e.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"B站视频链接 *"}),(0,l.jsx)("input",{type:"url",required:!0,placeholder:"https://www.bilibili.com/video/BV...",value:p.bilibili_url,onChange:e=>g({...p,bilibili_url:e.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"支持格式：https://www.bilibili.com/video/BV... 或 https://b23.tv/..."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"缩略图URL"}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)("input",{type:"url",placeholder:"/thumbnails/video.jpg 或点击自动获取",value:p.thumbnail_url,onChange:e=>g({...p,thumbnail_url:e.target.value}),className:"flex-1 bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"}),(0,l.jsx)("button",{type:"button",onClick:j,disabled:b||!p.bilibili_url,className:"px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white rounded transition-colors duration-200 whitespace-nowrap",children:b?"获取中...":"自动获取"})]}),(0,l.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"留空将自动从B站获取缩略图，或手动输入图片URL"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"描述"}),(0,l.jsx)("textarea",{rows:3,value:p.description,onChange:e=>g({...p,description:e.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsx)("button",{type:"submit",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded",children:o?"更新":"添加"}),(0,l.jsx)("button",{type:"button",onClick:()=>{n(!1),c(null),g({title:"",artist:"",description:"",bilibili_url:"",thumbnail_url:""})},className:"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded",children:"取消"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[e.map(e=>{var t;return(0,l.jsxs)("div",{className:"bg-gray-700 p-4 rounded-lg flex items-start justify-between",children:[(0,l.jsxs)("div",{className:"flex items-start space-x-4 flex-1",children:[(0,l.jsxs)("div",{className:"flex-shrink-0",children:[e.thumbnail_url?(0,l.jsx)("img",{src:(t=e.thumbnail_url)?t.includes("bilibili.com")||t.includes("hdslb.com")?"/api/proxy-image?url=".concat(encodeURIComponent(t)):t:"",alt:e.title,className:"w-20 h-12 object-cover rounded",onError:e=>{var t;let a=e.target;a.style.display="none",null==(t=a.nextElementSibling)||t.classList.remove("hidden")}}):null,(0,l.jsx)("div",{className:"w-20 h-12 bg-gray-600 rounded flex items-center justify-center ".concat(e.thumbnail_url?"hidden":""),children:(0,l.jsx)(m.A,{className:"text-gray-400",size:16})})]}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("h3",{className:"font-semibold",children:e.title}),(0,l.jsxs)("p",{className:"text-gray-400 text-sm",children:["演唱：",e.artist]}),e.description&&(0,l.jsx)("p",{className:"text-gray-500 text-sm mt-1 line-clamp-2",children:e.description}),(0,l.jsxs)("div",{className:"flex items-center mt-2 space-x-4",children:[(0,l.jsx)("a",{href:e.bilibili_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 text-sm",children:"在B站观看 →"}),(0,l.jsx)("span",{className:"text-xs text-gray-500",children:new Date(e.created_at).toLocaleDateString("zh-CN")})]})]})]}),(0,l.jsxs)("div",{className:"flex space-x-2 ml-4",children:[(0,l.jsx)("button",{onClick:()=>{c(e),g({title:e.title,artist:e.artist,description:e.description||"",bilibili_url:e.bilibili_url,thumbnail_url:e.thumbnail_url||""}),n(!0)},className:"text-blue-400 hover:text-blue-300 p-2",title:"编辑",children:(0,l.jsx)(u,{size:20})}),(0,l.jsx)("button",{onClick:()=>v(e.id),className:"text-red-400 hover:text-red-300 p-2",title:"删除",children:(0,l.jsx)(x,{size:20})})]})]},e.id)}),0===e.length&&(0,l.jsx)("div",{className:"text-center py-8 text-gray-400",children:"暂无音乐视频"})]})]})}function p(){let[e,t]=(0,r.useState)([]),[a,s]=(0,r.useState)(!0),[i,n]=(0,r.useState)(!1),[c,m]=(0,r.useState)(null),[b,h]=(0,r.useState)({title:"",description:"",poster_url:"",movie_link:"",video_file_path:"",category:"drama",rating:"",release_year:""}),[p,g]=(0,r.useState)(!1),[y,f]=(0,r.useState)(0),v=[{id:"gangster",label:"黑帮类"},{id:"romance",label:"情感类"},{id:"history",label:"历史类"},{id:"thriller",label:"惊悚类"},{id:"drama",label:"剧情类"},{id:"action",label:"动作类"}];(0,r.useEffect)(()=>{j()},[]);let j=async()=>{try{let e=btoa("".concat(localStorage.getItem("admin_username"),":").concat(localStorage.getItem("admin_password"))),a=await fetch("/api/admin/movies",{headers:{Authorization:"Basic ".concat(e)}});if(a.ok){let e=await a.json();t(e.data)}}catch(e){console.error("Failed to fetch movies:",e)}finally{s(!1)}},N=async e=>{e.preventDefault();try{let e=btoa("".concat(localStorage.getItem("admin_username"),":").concat(localStorage.getItem("admin_password"))),t=c?"PUT":"POST",a={...b,rating:b.rating?parseFloat(b.rating):null,release_year:b.release_year?parseInt(b.release_year):null,...c&&{id:c.id}},l=await fetch("/api/admin/movies",{method:t,headers:{"Content-Type":"application/json",Authorization:"Basic ".concat(e)},body:JSON.stringify(a)});if(l.ok)n(!1),m(null),h({title:"",description:"",poster_url:"",movie_link:"",category:"drama",rating:"",release_year:""}),j();else{let e=await l.json();alert(e.error||"操作失败，请检查输入信息")}}catch(e){console.error("Failed to submit:",e),alert("操作失败")}},w=async e=>{if(confirm("确定要删除这部电影吗？"))try{let t=btoa("".concat(localStorage.getItem("admin_username"),":").concat(localStorage.getItem("admin_password")));(await fetch("/api/admin/movies?id=".concat(e),{method:"DELETE",headers:{Authorization:"Basic ".concat(t)}})).ok?j():alert("删除失败")}catch(e){console.error("Failed to delete movie:",e),alert("删除失败")}},_=async e=>{var t;let a=null==(t=e.target.files)?void 0:t[0];if(a){g(!0),f(0);try{let e=new FormData;e.append("video",a);let t=btoa("".concat(localStorage.getItem("admin_username"),":").concat(localStorage.getItem("admin_password"))),l=setInterval(()=>{f(e=>Math.min(e+10,90))},200),r=await fetch("/api/upload/video",{method:"POST",headers:{Authorization:"Basic ".concat(t)},body:e});if(clearInterval(l),f(100),r.ok){let e=await r.json();h(t=>({...t,video_file_path:e.data.filePath})),alert("视频上传成功！")}else{let e=await r.json();alert("上传失败："+(e.error||"未知错误"))}}catch(e){console.error("Upload error:",e),alert("上传失败，请检查网络连接")}finally{g(!1),f(0)}}};return a?(0,l.jsx)("div",{className:"text-center py-8",children:"加载中..."}):(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,l.jsx)("h2",{className:"text-2xl font-semibold",children:"电影管理"}),(0,l.jsxs)("button",{onClick:()=>n(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center",children:[(0,l.jsx)(d,{className:"mr-2",size:20}),"添加电影"]})]}),i&&(0,l.jsxs)("div",{className:"bg-gray-700 p-6 rounded-lg mb-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold mb-4",children:c?"编辑电影":"添加新电影"}),(0,l.jsxs)("form",{onSubmit:N,className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"电影名称 *"}),(0,l.jsx)("input",{type:"text",required:!0,value:b.title,onChange:e=>h({...b,title:e.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"分类 *"}),(0,l.jsx)("select",{required:!0,value:b.category,onChange:e=>h({...b,category:e.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500",children:v.map(e=>(0,l.jsx)("option",{value:e.id,children:e.label},e.id))})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"电影描述 *"}),(0,l.jsx)("textarea",{rows:4,required:!0,value:b.description,onChange:e=>h({...b,description:e.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"海报图片URL"}),(0,l.jsx)("input",{type:"url",placeholder:"/posters/movie.jpg",value:b.poster_url,onChange:e=>h({...b,poster_url:e.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"观看链接"}),(0,l.jsx)("input",{type:"url",placeholder:"https://example.com/movie",value:b.movie_link,onChange:e=>h({...b,movie_link:e.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"视频文件上传"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("input",{type:"file",accept:"video/mp4,video/quicktime,video/x-msvideo,video/webm,video/x-ms-wmv,video/x-flv",onChange:_,disabled:p,className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-600 file:text-white hover:file:bg-blue-700 disabled:opacity-50"}),p&&(0,l.jsx)("div",{className:"w-full bg-gray-600 rounded-full h-2",children:(0,l.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(y,"%")}})}),b.video_file_path&&(0,l.jsxs)("div",{className:"text-sm text-green-400",children:["✓ 已上传视频文件: ",b.video_file_path.split("/").pop()]}),(0,l.jsx)("p",{className:"text-xs text-gray-400",children:"支持格式：MP4, MOV, AVI, WebM, WMV, FLV（最大500MB）"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"评分 (0-10)"}),(0,l.jsx)("input",{type:"number",min:"0",max:"10",step:"0.1",placeholder:"8.5",value:b.rating,onChange:e=>h({...b,rating:e.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"上映年份"}),(0,l.jsx)("input",{type:"number",min:"1900",max:new Date().getFullYear(),placeholder:"2023",value:b.release_year,onChange:e=>h({...b,release_year:e.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-4",children:[(0,l.jsx)("button",{type:"submit",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded",children:c?"更新":"添加"}),(0,l.jsx)("button",{type:"button",onClick:()=>{n(!1),m(null),h({title:"",description:"",poster_url:"",movie_link:"",video_file_path:"",category:"drama",rating:"",release_year:""})},className:"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded",children:"取消"})]})]})]}),(0,l.jsxs)("div",{className:"grid gap-4",children:[e.map(e=>{var t;return(0,l.jsxs)("div",{className:"bg-gray-700 p-4 rounded-lg flex items-start justify-between",children:[(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:e.poster_url?(0,l.jsx)("img",{src:e.poster_url,alt:e.title,className:"w-16 h-24 object-cover rounded"}):(0,l.jsx)("div",{className:"w-16 h-24 bg-gray-600 rounded flex items-center justify-center",children:(0,l.jsx)(o.A,{className:"text-gray-400",size:24})})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("h3",{className:"font-semibold text-lg",children:e.title}),(0,l.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-400 mb-2",children:[(0,l.jsx)("span",{className:"px-2 py-1 bg-gray-600 rounded text-xs",children:null==(t=v.find(t=>t.id===e.category))?void 0:t.label}),e.rating&&(0,l.jsxs)("span",{className:"flex items-center",children:["⭐ ",e.rating]}),e.release_year&&(0,l.jsx)("span",{children:e.release_year})]}),(0,l.jsx)("p",{className:"text-gray-300 text-sm line-clamp-3 mb-2",children:e.description}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[e.movie_link&&(0,l.jsx)("a",{href:e.movie_link,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 text-sm",children:"观看电影 →"}),(0,l.jsx)("span",{className:"text-xs text-gray-500",children:new Date(e.created_at).toLocaleDateString("zh-CN")})]})]})]})}),(0,l.jsxs)("div",{className:"flex space-x-2 ml-4",children:[(0,l.jsx)("button",{onClick:()=>{m(e),h({title:e.title,description:e.description,poster_url:e.poster_url||"",movie_link:e.movie_link||"",video_file_path:e.video_file_path||"",category:e.category,rating:e.rating?e.rating.toString():"",release_year:e.release_year?e.release_year.toString():""}),n(!0)},className:"text-blue-400 hover:text-blue-300 p-2",title:"编辑",children:(0,l.jsx)(u,{size:20})}),(0,l.jsx)("button",{onClick:()=>w(e.id),className:"text-red-400 hover:text-red-300 p-2",title:"删除",children:(0,l.jsx)(x,{size:20})})]})]},e.id)}),0===e.length&&(0,l.jsx)("div",{className:"text-center py-8 text-gray-400",children:"暂无电影信息"})]})]})}function g(){return(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"个人信息管理"}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"姓名"}),(0,l.jsx)("input",{type:"text",defaultValue:"马君",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-2",children:"个人简介"}),(0,l.jsx)("textarea",{rows:4,defaultValue:"音乐创作者 \xb7 视频制作人",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,l.jsx)("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg",children:"保存更改"})]})]})}},9803:(e,t,a)=>{"use strict";a.d(t,{A:()=>l});let l=(0,a(9946).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},9946:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var l=a(2115);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},s=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,l.forwardRef)((e,t)=>{let{color:a="currentColor",size:r=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:m,...u}=e;return(0,l.createElement)("svg",{ref:t,...i,width:r,height:r,stroke:a,strokeWidth:o?24*Number(n)/Number(r):n,className:s("lucide",c),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(u)&&{"aria-hidden":"true"},...u},[...m.map(e=>{let[t,a]=e;return(0,l.createElement)(t,a)}),...Array.isArray(d)?d:[d]])}),o=(e,t)=>{let a=(0,l.forwardRef)((a,i)=>{let{className:o,...c}=a;return(0,l.createElement)(n,{ref:i,iconNode:t,className:s("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),o),...c})});return a.displayName=r(e),a}}},e=>{e.O(0,[441,964,358],()=>e(e.s=2108)),_N_E=e.O()}]);