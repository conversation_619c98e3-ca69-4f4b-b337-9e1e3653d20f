{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/test/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport default function TestPage() {\n  const [musicData, setMusicData] = useState<any[]>([])\n  const [movieData, setMovieData] = useState<any[]>([])\n  const [adminMusicData, setAdminMusicData] = useState<any[]>([])\n  const [adminMovieData, setAdminMovieData] = useState<any[]>([])\n  const [testResults, setTestResults] = useState<string[]>([])\n\n  useEffect(() => {\n    runTests()\n  }, [])\n\n  const addTestResult = (message: string) => {\n    setTestResults(prev => [...prev, message])\n  }\n\n  const runTests = async () => {\n    addTestResult('🧪 开始测试数据同步...')\n\n    try {\n      // 1. 测试公开API\n      addTestResult('1. 测试公开API')\n      \n      const musicResponse = await fetch('/api/music')\n      const musicData = await musicResponse.json()\n      setMusicData(musicData.data)\n      addTestResult(`Music API 返回: ${musicData.data.length} 个视频`)\n      \n      const movieResponse = await fetch('/api/movies')\n      const movieData = await movieResponse.json()\n      setMovieData(movieData.data)\n      addTestResult(`Movie API 返回: ${movieData.data.length} 部电影`)\n\n      // 2. 测试管理API（需要认证）\n      addTestResult('2. 测试管理API')\n      \n      const credentials = btoa('admin:admin123')\n      const authHeaders = {\n        'Authorization': `Basic ${credentials}`\n      }\n\n      const adminMusicResponse = await fetch('/api/admin/music', {\n        headers: authHeaders\n      })\n      const adminMusicData = await adminMusicResponse.json()\n      setAdminMusicData(adminMusicData.data)\n      addTestResult(`Admin Music API 返回: ${adminMusicData.data.length} 个视频`)\n\n      const adminMovieResponse = await fetch('/api/admin/movies', {\n        headers: authHeaders\n      })\n      const adminMovieData = await adminMovieResponse.json()\n      setAdminMovieData(adminMovieData.data)\n      addTestResult(`Admin Movie API 返回: ${adminMovieData.data.length} 部电影`)\n\n      // 3. 验证数据一致性\n      addTestResult('3. 验证数据一致性')\n      const musicConsistent = musicData.data.length === adminMusicData.data.length\n      const movieConsistent = movieData.data.length === adminMovieData.data.length\n      \n      addTestResult(`Music数据一致性: ${musicConsistent ? '✅' : '❌'}`)\n      addTestResult(`Movie数据一致性: ${movieConsistent ? '✅' : '❌'}`)\n      \n      addTestResult('✅ 测试完成')\n\n    } catch (error) {\n      addTestResult(`❌ 测试失败: ${error}`)\n    }\n  }\n\n  const testAddMusic = async () => {\n    try {\n      const credentials = btoa('admin:admin123')\n      const response = await fetch('/api/admin/music', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Basic ${credentials}`\n        },\n        body: JSON.stringify({\n          title: '测试音乐视频',\n          artist: 'KIMAHALA',\n          description: '这是一个测试视频',\n          bilibili_url: 'https://www.bilibili.com/video/BV1234567890',\n          thumbnail_url: '/thumbnails/test.jpg'\n        })\n      })\n\n      if (response.ok) {\n        addTestResult('✅ 成功添加测试音乐视频')\n        // 重新获取数据\n        setTimeout(() => {\n          window.location.reload()\n        }, 1000)\n      } else {\n        addTestResult('❌ 添加音乐视频失败')\n      }\n    } catch (error) {\n      addTestResult(`❌ 添加音乐视频错误: ${error}`)\n    }\n  }\n\n  const testAddMovie = async () => {\n    try {\n      const credentials = btoa('admin:admin123')\n      const response = await fetch('/api/admin/movies', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Basic ${credentials}`\n        },\n        body: JSON.stringify({\n          title: '测试电影',\n          description: '这是一个测试电影',\n          category: 'drama',\n          rating: 8.5,\n          release_year: 2024\n        })\n      })\n\n      if (response.ok) {\n        addTestResult('✅ 成功添加测试电影')\n        // 重新获取数据\n        setTimeout(() => {\n          window.location.reload()\n        }, 1000)\n      } else {\n        addTestResult('❌ 添加电影失败')\n      }\n    } catch (error) {\n      addTestResult(`❌ 添加电影错误: ${error}`)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 text-white p-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        <h1 className=\"text-3xl font-bold mb-8\">数据同步测试页面</h1>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-8\">\n          <div className=\"bg-gray-800 p-6 rounded-lg\">\n            <h2 className=\"text-xl font-semibold mb-4\">测试操作</h2>\n            <div className=\"space-y-4\">\n              <button\n                onClick={testAddMusic}\n                className=\"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded\"\n              >\n                添加测试音乐视频\n              </button>\n              <button\n                onClick={testAddMovie}\n                className=\"w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded\"\n              >\n                添加测试电影\n              </button>\n              <button\n                onClick={() => window.location.reload()}\n                className=\"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded\"\n              >\n                重新测试\n              </button>\n            </div>\n          </div>\n\n          <div className=\"bg-gray-800 p-6 rounded-lg\">\n            <h2 className=\"text-xl font-semibold mb-4\">数据统计</h2>\n            <div className=\"space-y-2\">\n              <p>公开Music API: {musicData.length} 个视频</p>\n              <p>管理Music API: {adminMusicData.length} 个视频</p>\n              <p>公开Movie API: {movieData.length} 部电影</p>\n              <p>管理Movie API: {adminMovieData.length} 部电影</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-gray-800 p-6 rounded-lg\">\n          <h2 className=\"text-xl font-semibold mb-4\">测试日志</h2>\n          <div className=\"bg-gray-900 p-4 rounded font-mono text-sm max-h-96 overflow-y-auto\">\n            {testResults.map((result, index) => (\n              <div key={index} className=\"mb-1\">\n                {result}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"mt-8 text-center\">\n          <p className=\"text-gray-400\">\n            测试完成后，请访问 <a href=\"/music\" className=\"text-blue-400 hover:underline\">/music</a> 和 <a href=\"/movie\" className=\"text-blue-400 hover:underline\">/movie</a> 页面验证数据同步\n          </p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,eAAe,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;IAC3C;IAEA,MAAM,WAAW;QACf,cAAc;QAEd,IAAI;YACF,aAAa;YACb,cAAc;YAEd,MAAM,gBAAgB,MAAM,MAAM;YAClC,MAAM,YAAY,MAAM,cAAc,IAAI;YAC1C,aAAa,UAAU,IAAI;YAC3B,cAAc,AAAC,iBAAsC,OAAtB,UAAU,IAAI,CAAC,MAAM,EAAC;YAErD,MAAM,gBAAgB,MAAM,MAAM;YAClC,MAAM,YAAY,MAAM,cAAc,IAAI;YAC1C,aAAa,UAAU,IAAI;YAC3B,cAAc,AAAC,iBAAsC,OAAtB,UAAU,IAAI,CAAC,MAAM,EAAC;YAErD,mBAAmB;YACnB,cAAc;YAEd,MAAM,cAAc,KAAK;YACzB,MAAM,cAAc;gBAClB,iBAAiB,AAAC,SAAoB,OAAZ;YAC5B;YAEA,MAAM,qBAAqB,MAAM,MAAM,oBAAoB;gBACzD,SAAS;YACX;YACA,MAAM,iBAAiB,MAAM,mBAAmB,IAAI;YACpD,kBAAkB,eAAe,IAAI;YACrC,cAAc,AAAC,uBAAiD,OAA3B,eAAe,IAAI,CAAC,MAAM,EAAC;YAEhE,MAAM,qBAAqB,MAAM,MAAM,qBAAqB;gBAC1D,SAAS;YACX;YACA,MAAM,iBAAiB,MAAM,mBAAmB,IAAI;YACpD,kBAAkB,eAAe,IAAI;YACrC,cAAc,AAAC,uBAAiD,OAA3B,eAAe,IAAI,CAAC,MAAM,EAAC;YAEhE,aAAa;YACb,cAAc;YACd,MAAM,kBAAkB,UAAU,IAAI,CAAC,MAAM,KAAK,eAAe,IAAI,CAAC,MAAM;YAC5E,MAAM,kBAAkB,UAAU,IAAI,CAAC,MAAM,KAAK,eAAe,IAAI,CAAC,MAAM;YAE5E,cAAc,AAAC,eAA0C,OAA5B,kBAAkB,MAAM;YACrD,cAAc,AAAC,eAA0C,OAA5B,kBAAkB,MAAM;YAErD,cAAc;QAEhB,EAAE,OAAO,OAAO;YACd,cAAc,AAAC,WAAgB,OAAN;QAC3B;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,cAAc,KAAK;YACzB,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,SAAoB,OAAZ;gBAC5B;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;oBACP,QAAQ;oBACR,aAAa;oBACb,cAAc;oBACd,eAAe;gBACjB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,cAAc;gBACd,SAAS;gBACT,WAAW;oBACT,OAAO,QAAQ,CAAC,MAAM;gBACxB,GAAG;YACL,OAAO;gBACL,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,cAAc,AAAC,eAAoB,OAAN;QAC/B;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,cAAc,KAAK;YACzB,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,AAAC,SAAoB,OAAZ;gBAC5B;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,QAAQ;oBACR,cAAc;gBAChB;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,cAAc;gBACd,SAAS;gBACT,WAAW;oBACT,OAAO,QAAQ,CAAC,MAAM;gBACxB,GAAG;YACL,OAAO;gBACL,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,cAAc,AAAC,aAAkB,OAAN;QAC7B;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA0B;;;;;;8BAExC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4CACrC,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAML,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDAAE;gDAAc,UAAU,MAAM;gDAAC;;;;;;;sDAClC,6LAAC;;gDAAE;gDAAc,eAAe,MAAM;gDAAC;;;;;;;sDACvC,6LAAC;;gDAAE;gDAAc,UAAU,MAAM;gDAAC;;;;;;;sDAClC,6LAAC;;gDAAE;gDAAc,eAAe,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;8BAK7C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,QAAQ,sBACxB,6LAAC;oCAAgB,WAAU;8CACxB;mCADO;;;;;;;;;;;;;;;;8BAOhB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAgB;0CACjB,6LAAC;gCAAE,MAAK;gCAAS,WAAU;0CAAgC;;;;;;4BAAU;0CAAG,6LAAC;gCAAE,MAAK;gCAAS,WAAU;0CAAgC;;;;;;4BAAU;;;;;;;;;;;;;;;;;;;;;;;AAMnK;GAjMwB;KAAA", "debugId": null}}]}