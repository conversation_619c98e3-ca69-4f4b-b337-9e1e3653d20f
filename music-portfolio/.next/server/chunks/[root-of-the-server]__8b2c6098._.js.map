{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\n\nexport interface AdminSession {\n  isAuthenticated: boolean\n  username?: string\n}\n\nexport function checkAdminAuth(request: NextRequest): AdminSession {\n  const authHeader = request.headers.get('authorization')\n  \n  if (!authHeader || !authHeader.startsWith('Basic ')) {\n    return { isAuthenticated: false }\n  }\n\n  try {\n    const base64Credentials = authHeader.slice(6)\n    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii')\n    const [username, password] = credentials.split(':')\n\n    const adminUsername = process.env.ADMIN_USERNAME || 'admin'\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'\n\n    if (username === adminUsername && password === adminPassword) {\n      return { isAuthenticated: true, username }\n    }\n\n    return { isAuthenticated: false }\n  } catch (error) {\n    return { isAuthenticated: false }\n  }\n}\n\nexport function requireAuth() {\n  return new Response('Unauthorized', {\n    status: 401,\n    headers: {\n      'WWW-Authenticate': 'Basic realm=\"Admin Area\"',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAOO,SAAS,eAAe,OAAoB;IACjD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,WAAW;QACnD,OAAO;YAAE,iBAAiB;QAAM;IAClC;IAEA,IAAI;QACF,MAAM,oBAAoB,WAAW,KAAK,CAAC;QAC3C,MAAM,cAAc,OAAO,IAAI,CAAC,mBAAmB,UAAU,QAAQ,CAAC;QACtE,MAAM,CAAC,UAAU,SAAS,GAAG,YAAY,KAAK,CAAC;QAE/C,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QACpD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEpD,IAAI,aAAa,iBAAiB,aAAa,eAAe;YAC5D,OAAO;gBAAE,iBAAiB;gBAAM;YAAS;QAC3C;QAEA,OAAO;YAAE,iBAAiB;QAAM;IAClC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,iBAAiB;QAAM;IAClC;AACF;AAEO,SAAS;IACd,OAAO,IAAI,SAAS,gBAAgB;QAClC,QAAQ;QACR,SAAS;YACP,oBAAoB;QACtB;IACF;AACF", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/admin/movies/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { checkAdminAuth, requireAuth } from '@/lib/auth'\n\n// 模拟数据存储 - 实际应用中应该使用数据库\nlet movies = [\n  {\n    id: '1',\n    title: '教父',\n    description: '一个黑手党家族的传奇故事，展现了权力、家族和忠诚的复杂关系。弗朗西斯·福特·科波拉执导的经典之作，被誉为电影史上最伟大的作品之一。',\n    poster_url: '/posters/godfather.jpg',\n    movie_link: 'https://example.com/godfather',\n    category: 'gangster',\n    rating: 9.3,\n    release_year: 1972,\n    created_at: '2024-01-15T00:00:00Z',\n    updated_at: '2024-01-15T00:00:00Z',\n  },\n  {\n    id: '2',\n    title: '肖申克的救赎',\n    description: '一个关于希望、友谊和救赎的感人故事。安迪在监狱中的经历告诉我们，即使在最黑暗的地方，希望之光也永远不会熄灭。',\n    poster_url: '/posters/shawshank.jpg',\n    movie_link: 'https://example.com/shawshank',\n    category: 'drama',\n    rating: 9.7,\n    release_year: 1994,\n    created_at: '2024-01-14T00:00:00Z',\n    updated_at: '2024-01-14T00:00:00Z',\n  },\n]\n\n// 获取所有电影\nexport async function GET(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  return NextResponse.json({ \n    success: true, \n    data: movies \n  })\n}\n\n// 创建新电影\nexport async function POST(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  try {\n    const body = await request.json()\n    const { title, description, poster_url, movie_link, category, rating, release_year } = body\n\n    // 验证必填字段\n    if (!title || !description || !category) {\n      return NextResponse.json(\n        { error: 'Title, description, and category are required' },\n        { status: 400 }\n      )\n    }\n\n    // 验证分类\n    const validCategories = ['gangster', 'romance', 'history', 'thriller', 'drama', 'action']\n    if (!validCategories.includes(category)) {\n      return NextResponse.json(\n        { error: 'Invalid category' },\n        { status: 400 }\n      )\n    }\n\n    // 验证评分\n    if (rating && (rating < 0 || rating > 10)) {\n      return NextResponse.json(\n        { error: 'Rating must be between 0 and 10' },\n        { status: 400 }\n      )\n    }\n\n    // 验证年份\n    if (release_year && (release_year < 1900 || release_year > new Date().getFullYear())) {\n      return NextResponse.json(\n        { error: 'Invalid release year' },\n        { status: 400 }\n      )\n    }\n\n    const newMovie = {\n      id: Date.now().toString(),\n      title,\n      description,\n      poster_url: poster_url || null,\n      movie_link: movie_link || null,\n      category,\n      rating: rating || null,\n      release_year: release_year || null,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    }\n\n    movies.push(newMovie)\n\n    return NextResponse.json({ \n      success: true, \n      data: newMovie \n    })\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Invalid request body' },\n      { status: 400 }\n    )\n  }\n}\n\n// 更新电影\nexport async function PUT(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  try {\n    const body = await request.json()\n    const { id, title, description, poster_url, movie_link, category, rating, release_year } = body\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'Movie ID is required' },\n        { status: 400 }\n      )\n    }\n\n    const movieIndex = movies.findIndex(movie => movie.id === id)\n    if (movieIndex === -1) {\n      return NextResponse.json(\n        { error: 'Movie not found' },\n        { status: 404 }\n      )\n    }\n\n    // 验证必填字段\n    if (!title || !description || !category) {\n      return NextResponse.json(\n        { error: 'Title, description, and category are required' },\n        { status: 400 }\n      )\n    }\n\n    // 验证分类\n    const validCategories = ['gangster', 'romance', 'history', 'thriller', 'drama', 'action']\n    if (!validCategories.includes(category)) {\n      return NextResponse.json(\n        { error: 'Invalid category' },\n        { status: 400 }\n      )\n    }\n\n    // 验证评分\n    if (rating && (rating < 0 || rating > 10)) {\n      return NextResponse.json(\n        { error: 'Rating must be between 0 and 10' },\n        { status: 400 }\n      )\n    }\n\n    // 验证年份\n    if (release_year && (release_year < 1900 || release_year > new Date().getFullYear())) {\n      return NextResponse.json(\n        { error: 'Invalid release year' },\n        { status: 400 }\n      )\n    }\n\n    const updatedMovie = {\n      ...movies[movieIndex],\n      title,\n      description,\n      poster_url: poster_url || null,\n      movie_link: movie_link || null,\n      category,\n      rating: rating || null,\n      release_year: release_year || null,\n      updated_at: new Date().toISOString(),\n    }\n\n    movies[movieIndex] = updatedMovie\n\n    return NextResponse.json({ \n      success: true, \n      data: updatedMovie \n    })\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Invalid request body' },\n      { status: 400 }\n    )\n  }\n}\n\n// 删除电影\nexport async function DELETE(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  try {\n    const { searchParams } = new URL(request.url)\n    const id = searchParams.get('id')\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'Movie ID is required' },\n        { status: 400 }\n      )\n    }\n\n    const movieIndex = movies.findIndex(movie => movie.id === id)\n    if (movieIndex === -1) {\n      return NextResponse.json(\n        { error: 'Movie not found' },\n        { status: 404 }\n      )\n    }\n\n    const deletedMovie = movies[movieIndex]\n    movies.splice(movieIndex, 1)\n\n    return NextResponse.json({ \n      success: true, \n      data: deletedMovie \n    })\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Failed to delete movie' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,wBAAwB;AACxB,IAAI,SAAS;IACX;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,YAAY;IACd;CACD;AAGM,eAAe,IAAI,OAAoB;IAC5C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,MAAM;IACR;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;QAEvF,SAAS;QACT,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgD,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,kBAAkB;YAAC;YAAY;YAAW;YAAW;YAAY;YAAS;SAAS;QACzF,IAAI,CAAC,gBAAgB,QAAQ,CAAC,WAAW;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,IAAI,UAAU,CAAC,SAAS,KAAK,SAAS,EAAE,GAAG;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,IAAI,gBAAgB,CAAC,eAAe,QAAQ,eAAe,IAAI,OAAO,WAAW,EAAE,GAAG;YACpF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW;YACf,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA;YACA,YAAY,cAAc;YAC1B,YAAY,cAAc;YAC1B;YACA,QAAQ,UAAU;YAClB,cAAc,gBAAgB;YAC9B,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,OAAO,IAAI,CAAC;QAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;QAE3F,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAC1D,IAAI,eAAe,CAAC,GAAG;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgD,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,kBAAkB;YAAC;YAAY;YAAW;YAAW;YAAY;YAAS;SAAS;QACzF,IAAI,CAAC,gBAAgB,QAAQ,CAAC,WAAW;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,IAAI,UAAU,CAAC,SAAS,KAAK,SAAS,EAAE,GAAG;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,IAAI,gBAAgB,CAAC,eAAe,QAAQ,eAAe,IAAI,OAAO,WAAW,EAAE,GAAG;YACpF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,eAAe;YACnB,GAAG,MAAM,CAAC,WAAW;YACrB;YACA;YACA,YAAY,cAAc;YAC1B,YAAY,cAAc;YAC1B;YACA,QAAQ,UAAU;YAClB,cAAc,gBAAgB;YAC9B,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,MAAM,CAAC,WAAW,GAAG;QAErB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;QAE5B,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAC1D,IAAI,eAAe,CAAC,GAAG;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,eAAe,MAAM,CAAC,WAAW;QACvC,OAAO,MAAM,CAAC,YAAY;QAE1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}