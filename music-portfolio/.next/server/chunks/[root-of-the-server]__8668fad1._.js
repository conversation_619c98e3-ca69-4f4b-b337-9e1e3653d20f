module.exports = {

"[project]/.next-internal/server/app/api/admin/music/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "checkAdminAuth": ()=>checkAdminAuth,
    "requireAuth": ()=>requireAuth
});
function checkAdminAuth(request) {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Basic ')) {
        return {
            isAuthenticated: false
        };
    }
    try {
        const base64Credentials = authHeader.slice(6);
        const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
        const [username, password] = credentials.split(':');
        const adminUsername = process.env.ADMIN_USERNAME || 'admin';
        const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
        if (username === adminUsername && password === adminPassword) {
            return {
                isAuthenticated: true,
                username
            };
        }
        return {
            isAuthenticated: false
        };
    } catch (error) {
        return {
            isAuthenticated: false
        };
    }
}
function requireAuth() {
    return new Response('Unauthorized', {
        status: 401,
        headers: {
            'WWW-Authenticate': 'Basic realm="Admin Area"'
        }
    });
}
}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/lib/simple-store.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// 简单的文件数据存储
__turbopack_context__.s({
    "addMovie": ()=>addMovie,
    "addMusicVideo": ()=>addMusicVideo,
    "deleteMovie": ()=>deleteMovie,
    "deleteMusicVideo": ()=>deleteMusicVideo,
    "getMovies": ()=>getMovies,
    "getMusicVideos": ()=>getMusicVideos,
    "updateMovie": ()=>updateMovie,
    "updateMusicVideo": ()=>updateMusicVideo
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
const DATA_DIR = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(process.cwd(), 'data');
const MUSIC_FILE = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(DATA_DIR, 'music.json');
const MOVIES_FILE = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["join"])(DATA_DIR, 'movies.json');
// 确保数据目录存在
function ensureDataDir() {
    try {
        const fs = __turbopack_context__.r("[externals]/fs [external] (fs, cjs)");
        if (!fs.existsSync(DATA_DIR)) {
            fs.mkdirSync(DATA_DIR, {
                recursive: true
            });
        }
    } catch (error) {
        console.error('Error creating data directory:', error);
    }
}
// 初始化数据文件
function initializeData() {
    ensureDataDir();
    // 初始化音乐数据（空数组）
    if (!(0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["existsSync"])(MUSIC_FILE)) {
        (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["writeFileSync"])(MUSIC_FILE, JSON.stringify([], null, 2));
    }
    // 初始化电影数据
    if (!(0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["existsSync"])(MOVIES_FILE)) {
        const initialMovies = [
            {
                id: '1',
                title: '教父',
                description: '一个黑手党家族的传奇故事，展现了权力、家族和忠诚的复杂关系。弗朗西斯·福特·科波拉执导的经典之作，被誉为电影史上最伟大的作品之一。',
                poster_url: '/posters/godfather.jpg',
                movie_link: 'https://example.com/godfather',
                category: 'gangster',
                rating: 9.3,
                release_year: 1972,
                created_at: '2024-01-15T00:00:00Z',
                updated_at: '2024-01-15T00:00:00Z'
            },
            {
                id: '2',
                title: '肖申克的救赎',
                description: '一个关于希望、友谊和救赎的感人故事。安迪在监狱中的经历告诉我们，即使在最黑暗的地方，希望之光也永远不会熄灭。',
                poster_url: '/posters/shawshank.jpg',
                movie_link: 'https://example.com/shawshank',
                category: 'drama',
                rating: 9.7,
                release_year: 1994,
                created_at: '2024-01-14T00:00:00Z',
                updated_at: '2024-01-14T00:00:00Z'
            },
            {
                id: '3',
                title: '泰坦尼克号',
                description: '一个跨越阶级的爱情故事，在历史上最著名的海难背景下展开。杰克和露丝的爱情故事感动了全世界观众。',
                poster_url: '/posters/titanic.jpg',
                movie_link: 'https://example.com/titanic',
                category: 'romance',
                rating: 7.9,
                release_year: 1997,
                created_at: '2024-01-13T00:00:00Z',
                updated_at: '2024-01-13T00:00:00Z'
            },
            {
                id: '4',
                title: '辛德勒的名单',
                description: '二战期间，德国商人奥斯卡·辛德勒拯救犹太人的真实故事。这部黑白电影以其深刻的人道主义精神震撼人心。',
                poster_url: '/posters/schindler.jpg',
                movie_link: 'https://example.com/schindler',
                category: 'history',
                rating: 9.0,
                release_year: 1993,
                created_at: '2024-01-12T00:00:00Z',
                updated_at: '2024-01-12T00:00:00Z'
            },
            {
                id: '5',
                title: '好家伙',
                description: '基于真实事件改编的黑帮电影，讲述了亨利·希尔在黑手党中的生活经历。马丁·斯科塞斯的又一经典之作。',
                poster_url: '/posters/goodfellas.jpg',
                movie_link: 'https://example.com/goodfellas',
                category: 'gangster',
                rating: 8.7,
                release_year: 1990,
                created_at: '2024-01-11T00:00:00Z',
                updated_at: '2024-01-11T00:00:00Z'
            },
            {
                id: '6',
                title: '沉默的羔羊',
                description: '一部心理惊悚片的经典之作，FBI学员克拉丽丝与食人魔汉尼拔博士的心理较量令人毛骨悚然。',
                poster_url: '/posters/silence.jpg',
                movie_link: 'https://example.com/silence',
                category: 'thriller',
                rating: 8.6,
                release_year: 1991,
                created_at: '2024-01-10T00:00:00Z',
                updated_at: '2024-01-10T00:00:00Z'
            },
            {
                id: '7',
                title: '阿甘正传',
                description: '一个智商不高但心地善良的男人的人生传奇。阿甘用他的纯真和坚持诠释了什么是真正的成功。',
                poster_url: '/posters/forrest.jpg',
                movie_link: 'https://example.com/forrest',
                category: 'drama',
                rating: 8.8,
                release_year: 1994,
                created_at: '2024-01-09T00:00:00Z',
                updated_at: '2024-01-09T00:00:00Z'
            },
            {
                id: '8',
                title: '勇敢的心',
                description: '苏格兰民族英雄威廉·华莱士为自由而战的史诗故事。梅尔·吉布森自导自演的历史巨作。',
                poster_url: '/posters/braveheart.jpg',
                movie_link: 'https://example.com/braveheart',
                category: 'history',
                rating: 8.4,
                release_year: 1995,
                created_at: '2024-01-08T00:00:00Z',
                updated_at: '2024-01-08T00:00:00Z'
            },
            {
                id: '9',
                title: '终结者2：审判日',
                description: '科幻动作片的里程碑之作，阿诺·施瓦辛格饰演的终结者成为了电影史上最经典的角色之一。',
                poster_url: '/posters/terminator2.jpg',
                movie_link: 'https://example.com/terminator2',
                category: 'action',
                rating: 8.5,
                release_year: 1991,
                created_at: '2024-01-07T00:00:00Z',
                updated_at: '2024-01-07T00:00:00Z'
            },
            {
                id: '10',
                title: '罗马假日',
                description: '奥黛丽·赫本和格利高里·派克主演的经典爱情喜剧，讲述了公主与记者在罗马的浪漫邂逅。',
                poster_url: '/posters/roman.jpg',
                movie_link: 'https://example.com/roman',
                category: 'romance',
                rating: 8.1,
                release_year: 1953,
                created_at: '2024-01-06T00:00:00Z',
                updated_at: '2024-01-06T00:00:00Z'
            }
        ];
        (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["writeFileSync"])(MOVIES_FILE, JSON.stringify(initialMovies, null, 2));
    }
}
function getMusicVideos() {
    try {
        initializeData();
        const data = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["readFileSync"])(MUSIC_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error reading music data:', error);
        return [];
    }
}
function addMusicVideo(video) {
    try {
        const videos = getMusicVideos();
        videos.push(video);
        (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["writeFileSync"])(MUSIC_FILE, JSON.stringify(videos, null, 2));
        return true;
    } catch (error) {
        console.error('Error adding music video:', error);
        return false;
    }
}
function updateMusicVideo(id, updatedVideo) {
    try {
        const videos = getMusicVideos();
        const index = videos.findIndex((v)=>v.id === id);
        if (index !== -1) {
            videos[index] = updatedVideo;
            (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["writeFileSync"])(MUSIC_FILE, JSON.stringify(videos, null, 2));
            return true;
        }
        return false;
    } catch (error) {
        console.error('Error updating music video:', error);
        return false;
    }
}
function deleteMusicVideo(id) {
    try {
        const videos = getMusicVideos();
        const index = videos.findIndex((v)=>v.id === id);
        if (index !== -1) {
            videos.splice(index, 1);
            (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["writeFileSync"])(MUSIC_FILE, JSON.stringify(videos, null, 2));
            return true;
        }
        return false;
    } catch (error) {
        console.error('Error deleting music video:', error);
        return false;
    }
}
function getMovies() {
    try {
        initializeData();
        const data = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["readFileSync"])(MOVIES_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error reading movie data:', error);
        return [];
    }
}
function addMovie(movie) {
    try {
        const movies = getMovies();
        movies.push(movie);
        (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["writeFileSync"])(MOVIES_FILE, JSON.stringify(movies, null, 2));
        return true;
    } catch (error) {
        console.error('Error adding movie:', error);
        return false;
    }
}
function updateMovie(id, updatedMovie) {
    try {
        const movies = getMovies();
        const index = movies.findIndex((m)=>m.id === id);
        if (index !== -1) {
            movies[index] = updatedMovie;
            (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["writeFileSync"])(MOVIES_FILE, JSON.stringify(movies, null, 2));
            return true;
        }
        return false;
    } catch (error) {
        console.error('Error updating movie:', error);
        return false;
    }
}
function deleteMovie(id) {
    try {
        const movies = getMovies();
        const index = movies.findIndex((m)=>m.id === id);
        if (index !== -1) {
            movies.splice(index, 1);
            (0, __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["writeFileSync"])(MOVIES_FILE, JSON.stringify(movies, null, 2));
            return true;
        }
        return false;
    } catch (error) {
        console.error('Error deleting movie:', error);
        return false;
    }
}
}),
"[project]/src/app/api/admin/music/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "DELETE": ()=>DELETE,
    "GET": ()=>GET,
    "POST": ()=>POST,
    "PUT": ()=>PUT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$simple$2d$store$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/simple-store.ts [app-route] (ecmascript)");
;
;
;
// 从B站URL中提取视频ID
function extractBilibiliId(url) {
    try {
        // 支持多种B站URL格式
        const patterns = [
            /bilibili\.com\/video\/(BV[a-zA-Z0-9]+)/,
            /bilibili\.com\/video\/(av\d+)/,
            /b23\.tv\/([a-zA-Z0-9]+)/
        ];
        for (const pattern of patterns){
            const match = url.match(pattern);
            if (match) {
                return match[1];
            }
        }
        return null;
    } catch (error) {
        return null;
    }
}
async function GET(request) {
    const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkAdminAuth"])(request);
    if (!auth.isAuthenticated) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requireAuth"])();
    }
    const musicVideos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$simple$2d$store$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getMusicVideos"])();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: true,
        data: musicVideos
    });
}
async function POST(request) {
    const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkAdminAuth"])(request);
    if (!auth.isAuthenticated) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requireAuth"])();
    }
    try {
        const body = await request.json();
        const { title, artist, description, bilibili_url, thumbnail_url } = body;
        if (!title || !artist || !bilibili_url) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Title, artist, and bilibili_url are required'
            }, {
                status: 400
            });
        }
        // 从B站URL中提取embed ID
        const bilibiliEmbedId = extractBilibiliId(bilibili_url);
        if (!bilibiliEmbedId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid Bilibili URL'
            }, {
                status: 400
            });
        }
        const newVideo = {
            id: Date.now().toString(),
            title,
            artist,
            description: description || '',
            bilibili_url,
            bilibili_embed_id: bilibiliEmbedId,
            thumbnail_url: thumbnail_url || '',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$simple$2d$store$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addMusicVideo"])(newVideo);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: newVideo
        });
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Invalid request body'
        }, {
            status: 400
        });
    }
}
async function PUT(request) {
    const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkAdminAuth"])(request);
    if (!auth.isAuthenticated) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requireAuth"])();
    }
    try {
        const body = await request.json();
        const { id, title, artist, description, bilibili_url, thumbnail_url } = body;
        if (!id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Video ID is required'
            }, {
                status: 400
            });
        }
        const musicVideos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$simple$2d$store$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getMusicVideos"])();
        const video = musicVideos.find((video)=>video.id === id);
        if (!video) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Video not found'
            }, {
                status: 404
            });
        }
        if (!title || !artist || !bilibili_url) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Title, artist, and bilibili_url are required'
            }, {
                status: 400
            });
        }
        // 从B站URL中提取embed ID
        const bilibiliEmbedId = extractBilibiliId(bilibili_url);
        if (!bilibiliEmbedId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid Bilibili URL'
            }, {
                status: 400
            });
        }
        const updatedVideo = {
            ...video,
            title,
            artist,
            description: description || '',
            bilibili_url,
            bilibili_embed_id: bilibiliEmbedId,
            thumbnail_url: thumbnail_url || '',
            updated_at: new Date().toISOString()
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$simple$2d$store$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateMusicVideo"])(id, updatedVideo);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: updatedVideo
        });
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Invalid request body'
        }, {
            status: 400
        });
    }
}
async function DELETE(request) {
    const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["checkAdminAuth"])(request);
    if (!auth.isAuthenticated) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["requireAuth"])();
    }
    try {
        const { searchParams } = new URL(request.url);
        const id = searchParams.get('id');
        if (!id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'ID is required'
            }, {
                status: 400
            });
        }
        const musicVideos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$simple$2d$store$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getMusicVideos"])();
        const video = musicVideos.find((video)=>video.id === id);
        if (!video) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Video not found'
            }, {
                status: 404
            });
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$simple$2d$store$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["deleteMusicVideo"])(id);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true
        });
    } catch (error) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to delete track'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8668fad1._.js.map