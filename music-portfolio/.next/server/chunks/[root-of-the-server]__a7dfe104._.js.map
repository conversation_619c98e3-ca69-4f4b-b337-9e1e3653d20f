{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\n\nexport interface AdminSession {\n  isAuthenticated: boolean\n  username?: string\n}\n\nexport function checkAdminAuth(request: NextRequest): AdminSession {\n  const authHeader = request.headers.get('authorization')\n  \n  if (!authHeader || !authHeader.startsWith('Basic ')) {\n    return { isAuthenticated: false }\n  }\n\n  try {\n    const base64Credentials = authHeader.slice(6)\n    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii')\n    const [username, password] = credentials.split(':')\n\n    const adminUsername = process.env.ADMIN_USERNAME || 'admin'\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'\n\n    if (username === adminUsername && password === adminPassword) {\n      return { isAuthenticated: true, username }\n    }\n\n    return { isAuthenticated: false }\n  } catch (error) {\n    return { isAuthenticated: false }\n  }\n}\n\nexport function requireAuth() {\n  return new Response('Unauthorized', {\n    status: 401,\n    headers: {\n      'WWW-Authenticate': 'Basic realm=\"Admin Area\"',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAOO,SAAS,eAAe,OAAoB;IACjD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,WAAW;QACnD,OAAO;YAAE,iBAAiB;QAAM;IAClC;IAEA,IAAI;QACF,MAAM,oBAAoB,WAAW,KAAK,CAAC;QAC3C,MAAM,cAAc,OAAO,IAAI,CAAC,mBAAmB,UAAU,QAAQ,CAAC;QACtE,MAAM,CAAC,UAAU,SAAS,GAAG,YAAY,KAAK,CAAC;QAE/C,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QACpD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEpD,IAAI,aAAa,iBAAiB,aAAa,eAAe;YAC5D,OAAO;gBAAE,iBAAiB;gBAAM;YAAS;QAC3C;QAEA,OAAO;YAAE,iBAAiB;QAAM;IAClC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,iBAAiB;QAAM;IAClC;AACF;AAEO,SAAS;IACd,OAAO,IAAI,SAAS,gBAAgB;QAClC,QAAQ;QACR,SAAS;YACP,oBAAoB;QACtB;IACF;AACF", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/admin/videos/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { checkAdminAuth, requireAuth } from '@/lib/auth'\n\n// 模拟数据存储 - 实际项目中应该使用数据库\nlet videos = [\n  {\n    id: '1',\n    title: '《夜空中最亮的星》创作过程分享',\n    description: '分享这首歌的创作灵感和制作过程，从词曲创作到录音制作的完整流程。',\n    bilibili_url: 'https://www.bilibili.com/video/BV1234567890',\n    bilibili_embed_id: 'BV1234567890',\n    thumbnail_url: '/thumbnails/video1.jpg',\n    created_at: '2024-01-15',\n    updated_at: '2024-01-15',\n  },\n  {\n    id: '2',\n    title: '吉他弹唱《时光倒流》',\n    description: '用吉他弹唱这首关于回忆的歌曲，希望能唤起大家心中美好的回忆。',\n    bilibili_url: 'https://www.bilibili.com/video/BV2345678901',\n    bilibili_embed_id: 'BV2345678901',\n    thumbnail_url: '/thumbnails/video2.jpg',\n    created_at: '2024-01-10',\n    updated_at: '2024-01-10',\n  },\n  {\n    id: '3',\n    title: '音乐制作软件使用教程',\n    description: '分享我常用的音乐制作软件和技巧，适合音乐制作初学者观看学习。',\n    bilibili_url: 'https://www.bilibili.com/video/BV3456789012',\n    bilibili_embed_id: 'BV3456789012',\n    thumbnail_url: '/thumbnails/video3.jpg',\n    created_at: '2024-01-05',\n    updated_at: '2024-01-05',\n  },\n]\n\n// 从B站链接提取BV号的函数\nfunction extractBVFromUrl(url: string): string {\n  const bvMatch = url.match(/BV[a-zA-Z0-9]+/)\n  return bvMatch ? bvMatch[0] : ''\n}\n\nexport async function GET(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  return NextResponse.json({ success: true, data: videos })\n}\n\nexport async function POST(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  try {\n    const body = await request.json()\n    const { title, description, bilibili_url, thumbnail_url } = body\n\n    if (!title || !bilibili_url) {\n      return NextResponse.json(\n        { error: 'Title and bilibili_url are required' },\n        { status: 400 }\n      )\n    }\n\n    const bilibili_embed_id = extractBVFromUrl(bilibili_url)\n    \n    if (!bilibili_embed_id) {\n      return NextResponse.json(\n        { error: 'Invalid Bilibili URL. Please provide a valid BV link.' },\n        { status: 400 }\n      )\n    }\n\n    const newVideo = {\n      id: Date.now().toString(),\n      title,\n      description: description || '',\n      bilibili_url,\n      bilibili_embed_id,\n      thumbnail_url: thumbnail_url || '',\n      created_at: new Date().toISOString().split('T')[0],\n      updated_at: new Date().toISOString().split('T')[0],\n    }\n\n    videos.push(newVideo)\n\n    return NextResponse.json({ success: true, data: newVideo })\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Invalid request body' },\n      { status: 400 }\n    )\n  }\n}\n\nexport async function DELETE(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  try {\n    const { searchParams } = new URL(request.url)\n    const id = searchParams.get('id')\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'ID is required' },\n        { status: 400 }\n      )\n    }\n\n    const index = videos.findIndex(video => video.id === id)\n    \n    if (index === -1) {\n      return NextResponse.json(\n        { error: 'Video not found' },\n        { status: 404 }\n      )\n    }\n\n    videos.splice(index, 1)\n\n    return NextResponse.json({ success: true })\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Failed to delete video' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,wBAAwB;AACxB,IAAI,SAAS;IACX;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,YAAY;QACZ,YAAY;IACd;CACD;AAED,gBAAgB;AAChB,SAAS,iBAAiB,GAAW;IACnC,MAAM,UAAU,IAAI,KAAK,CAAC;IAC1B,OAAO,UAAU,OAAO,CAAC,EAAE,GAAG;AAChC;AAEO,eAAe,IAAI,OAAoB;IAC5C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;QAAM,MAAM;IAAO;AACzD;AAEO,eAAe,KAAK,OAAoB;IAC7C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;QAE5D,IAAI,CAAC,SAAS,CAAC,cAAc;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsC,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,oBAAoB,iBAAiB;QAE3C,IAAI,CAAC,mBAAmB;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwD,GACjE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW;YACf,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA,aAAa,eAAe;YAC5B;YACA;YACA,eAAe,iBAAiB;YAChC,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAClD,YAAY,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACpD;QAEA,OAAO,IAAI,CAAC;QAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM,MAAM;QAAS;IAC3D,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,OAAO,OAAoB;IAC/C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;QAE5B,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAErD,IAAI,UAAU,CAAC,GAAG;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,MAAM,CAAC,OAAO;QAErB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}