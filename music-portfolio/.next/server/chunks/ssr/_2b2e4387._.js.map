{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Settings, Music, Video, User, Plus, Edit, Trash2 } from 'lucide-react'\n\nexport default function AdminPage() {\n  const [activeTab, setActiveTab] = useState('music')\n  const [isAuthenticated, setIsAuthenticated] = useState(false)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    // 检查认证状态\n    checkAuth()\n  }, [])\n\n  const checkAuth = async () => {\n    try {\n      const response = await fetch('/api/admin/auth', {\n        credentials: 'include'\n      })\n      setIsAuthenticated(response.ok)\n    } catch (error) {\n      setIsAuthenticated(false)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleLogin = () => {\n    const username = prompt('请输入用户名:')\n    const password = prompt('请输入密码:')\n    \n    if (username && password) {\n      const credentials = btoa(`${username}:${password}`)\n      fetch('/api/admin/auth', {\n        method: 'POST',\n        headers: {\n          'Authorization': `Basic ${credentials}`\n        }\n      }).then(response => {\n        if (response.ok) {\n          setIsAuthenticated(true)\n        } else {\n          alert('认证失败，请检查用户名和密码')\n        }\n      })\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"></div>\n          <p className=\"text-gray-400\">加载中...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"bg-gray-800 p-8 rounded-lg max-w-md w-full mx-4\">\n          <div className=\"text-center mb-8\">\n            <Settings className=\"mx-auto text-blue-400 mb-4\" size={48} />\n            <h1 className=\"text-2xl font-bold mb-2\">后台管理系统</h1>\n            <p className=\"text-gray-400\">请登录以访问管理功能</p>\n          </div>\n          \n          <button\n            onClick={handleLogin}\n            className=\"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200\"\n          >\n            登录\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  const tabs = [\n    { id: 'music', label: '音乐管理', icon: Music },\n    { id: 'video', label: '视频管理', icon: Video },\n    { id: 'profile', label: '个人信息', icon: User },\n  ]\n\n  return (\n    <div className=\"min-h-screen\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold mb-2\">后台管理系统</h1>\n          <p className=\"text-gray-400\">管理您的音乐和视频内容</p>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"border-b border-gray-700 mb-8\">\n          <nav className=\"flex space-x-8\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${\n                    activeTab === tab.id\n                      ? 'border-blue-500 text-blue-400'\n                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"mr-2\" size={20} />\n                  {tab.label}\n                </button>\n              )\n            })}\n          </nav>\n        </div>\n\n        {/* Content */}\n        <div className=\"bg-gray-800 rounded-lg p-6\">\n          {activeTab === 'music' && <MusicManagement />}\n          {activeTab === 'video' && <VideoManagement />}\n          {activeTab === 'profile' && <ProfileManagement />}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nfunction MusicManagement() {\n  const [musicTracks, setMusicTracks] = useState<any[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [showAddForm, setShowAddForm] = useState(false)\n  const [formData, setFormData] = useState({\n    title: '',\n    artist: '',\n    duration: '',\n    audio_url: '',\n    description: ''\n  })\n\n  useEffect(() => {\n    fetchMusicTracks()\n  }, [])\n\n  const fetchMusicTracks = async () => {\n    try {\n      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)\n      const response = await fetch('/api/admin/music', {\n        headers: {\n          'Authorization': `Basic ${credentials}`\n        }\n      })\n      if (response.ok) {\n        const data = await response.json()\n        setMusicTracks(data.data)\n      }\n    } catch (error) {\n      console.error('Failed to fetch music tracks:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleAddMusic = async (e: React.FormEvent) => {\n    e.preventDefault()\n    try {\n      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)\n      const response = await fetch('/api/admin/music', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Basic ${credentials}`\n        },\n        body: JSON.stringify(formData)\n      })\n\n      if (response.ok) {\n        setShowAddForm(false)\n        setFormData({ title: '', artist: '', duration: '', audio_url: '', description: '' })\n        fetchMusicTracks()\n      } else {\n        alert('添加失败，请检查输入信息')\n      }\n    } catch (error) {\n      console.error('Failed to add music:', error)\n      alert('添加失败')\n    }\n  }\n\n  const handleDeleteMusic = async (id: string) => {\n    if (!confirm('确定要删除这首音乐吗？')) return\n\n    try {\n      const credentials = btoa(`${localStorage.getItem('admin_username')}:${localStorage.getItem('admin_password')}`)\n      const response = await fetch(`/api/admin/music?id=${id}`, {\n        method: 'DELETE',\n        headers: {\n          'Authorization': `Basic ${credentials}`\n        }\n      })\n\n      if (response.ok) {\n        fetchMusicTracks()\n      } else {\n        alert('删除失败')\n      }\n    } catch (error) {\n      console.error('Failed to delete music:', error)\n      alert('删除失败')\n    }\n  }\n\n  if (isLoading) {\n    return <div className=\"text-center py-8\">加载中...</div>\n  }\n\n  return (\n    <div>\n      <div className=\"flex justify-between items-center mb-6\">\n        <h2 className=\"text-2xl font-semibold\">音乐作品管理</h2>\n        <button\n          onClick={() => setShowAddForm(true)}\n          className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center\"\n        >\n          <Plus className=\"mr-2\" size={20} />\n          添加音乐\n        </button>\n      </div>\n\n      {showAddForm && (\n        <div className=\"bg-gray-700 p-6 rounded-lg mb-6\">\n          <h3 className=\"text-lg font-semibold mb-4\">添加新音乐</h3>\n          <form onSubmit={handleAddMusic} className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">歌曲标题 *</label>\n              <input\n                type=\"text\"\n                required\n                value={formData.title}\n                onChange={(e) => setFormData({...formData, title: e.target.value})}\n                className=\"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">演唱者 *</label>\n              <input\n                type=\"text\"\n                required\n                value={formData.artist}\n                onChange={(e) => setFormData({...formData, artist: e.target.value})}\n                className=\"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">时长</label>\n              <input\n                type=\"text\"\n                placeholder=\"例如: 3:45\"\n                value={formData.duration}\n                onChange={(e) => setFormData({...formData, duration: e.target.value})}\n                className=\"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">音频文件URL *</label>\n              <input\n                type=\"url\"\n                required\n                placeholder=\"/music/song.mp3\"\n                value={formData.audio_url}\n                onChange={(e) => setFormData({...formData, audio_url: e.target.value})}\n                className=\"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium mb-2\">描述</label>\n              <textarea\n                rows={3}\n                value={formData.description}\n                onChange={(e) => setFormData({...formData, description: e.target.value})}\n                className=\"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500\"\n              />\n            </div>\n            <div className=\"flex space-x-4\">\n              <button\n                type=\"submit\"\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded\"\n              >\n                添加\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => setShowAddForm(false)}\n                className=\"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded\"\n              >\n                取消\n              </button>\n            </div>\n          </form>\n        </div>\n      )}\n\n      <div className=\"space-y-4\">\n        {musicTracks.map((track) => (\n          <div key={track.id} className=\"bg-gray-700 p-4 rounded-lg flex items-center justify-between\">\n            <div>\n              <h3 className=\"font-semibold\">{track.title}</h3>\n              <p className=\"text-gray-400 text-sm\">演唱：{track.artist} | 时长：{track.duration}</p>\n              {track.description && (\n                <p className=\"text-gray-500 text-sm mt-1\">{track.description}</p>\n              )}\n            </div>\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={() => handleDeleteMusic(track.id)}\n                className=\"text-red-400 hover:text-red-300 p-2\"\n              >\n                <Trash2 size={20} />\n              </button>\n            </div>\n          </div>\n        ))}\n\n        {musicTracks.length === 0 && (\n          <div className=\"text-center py-8 text-gray-400\">\n            暂无音乐作品\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\nfunction VideoManagement() {\n  return (\n    <div>\n      <div className=\"flex justify-between items-center mb-6\">\n        <h2 className=\"text-2xl font-semibold\">视频作品管理</h2>\n        <button className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center\">\n          <Plus className=\"mr-2\" size={20} />\n          添加视频\n        </button>\n      </div>\n      \n      <div className=\"space-y-4\">\n        <div className=\"bg-gray-700 p-4 rounded-lg flex items-center justify-between\">\n          <div>\n            <h3 className=\"font-semibold\">《夜空中最亮的星》创作过程分享</h3>\n            <p className=\"text-gray-400 text-sm\">发布时间：2024-01-15</p>\n          </div>\n          <div className=\"flex space-x-2\">\n            <button className=\"text-blue-400 hover:text-blue-300 p-2\">\n              <Edit size={20} />\n            </button>\n            <button className=\"text-red-400 hover:text-red-300 p-2\">\n              <Trash2 size={20} />\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nfunction ProfileManagement() {\n  return (\n    <div>\n      <h2 className=\"text-2xl font-semibold mb-6\">个人信息管理</h2>\n      \n      <div className=\"space-y-6\">\n        <div>\n          <label className=\"block text-sm font-medium mb-2\">姓名</label>\n          <input\n            type=\"text\"\n            defaultValue=\"马君\"\n            className=\"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500\"\n          />\n        </div>\n        \n        <div>\n          <label className=\"block text-sm font-medium mb-2\">个人简介</label>\n          <textarea\n            rows={4}\n            defaultValue=\"音乐创作者 · 视频制作人\"\n            className=\"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500\"\n          />\n        </div>\n        \n        <button className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg\">\n          保存更改\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;QACT;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,aAAa;YACf;YACA,mBAAmB,SAAS,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,mBAAmB;QACrB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,WAAW,OAAO;QACxB,MAAM,WAAW,OAAO;QAExB,IAAI,YAAY,UAAU;YACxB,MAAM,cAAc,KAAK,GAAG,SAAS,CAAC,EAAE,UAAU;YAClD,MAAM,mBAAmB;gBACvB,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,MAAM,EAAE,aAAa;gBACzC;YACF,GAAG,IAAI,CAAC,CAAA;gBACN,IAAI,SAAS,EAAE,EAAE;oBACf,mBAAmB;gBACrB,OAAO;oBACL,MAAM;gBACR;YACF;QACF;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;gCAA6B,MAAM;;;;;;0CACvD,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAG/B,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAS,OAAO;YAAQ,MAAM,oMAAA,CAAA,QAAK;QAAC;QAC1C;YAAE,IAAI;YAAS,OAAO;YAAQ,MAAM,oMAAA,CAAA,QAAK;QAAC;QAC1C;YAAE,IAAI;YAAW,OAAO;YAAQ,MAAM,kMAAA,CAAA,OAAI;QAAC;KAC5C;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCACxC,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC;4BACT,MAAM,OAAO,IAAI,IAAI;4BACrB,qBACE,8OAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,CAAC,2DAA2D,EACrE,cAAc,IAAI,EAAE,GAChB,kCACA,8EACJ;;kDAEF,8OAAC;wCAAK,WAAU;wCAAO,MAAM;;;;;;oCAC5B,IAAI,KAAK;;+BATL,IAAI,EAAE;;;;;wBAYjB;;;;;;;;;;;8BAKJ,8OAAC;oBAAI,WAAU;;wBACZ,cAAc,yBAAW,8OAAC;;;;;wBAC1B,cAAc,yBAAW,8OAAC;;;;;wBAC1B,cAAc,2BAAa,8OAAC;;;;;;;;;;;;;;;;;;;;;;AAKvC;AAEA,SAAS;IACP,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,QAAQ;QACR,UAAU;QACV,WAAW;QACX,aAAa;IACf;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,cAAc,KAAK,GAAG,aAAa,OAAO,CAAC,kBAAkB,CAAC,EAAE,aAAa,OAAO,CAAC,mBAAmB;YAC9G,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,SAAS;oBACP,iBAAiB,CAAC,MAAM,EAAE,aAAa;gBACzC;YACF;YACA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,KAAK,IAAI;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,cAAc,KAAK,GAAG,aAAa,OAAO,CAAC,kBAAkB,CAAC,EAAE,aAAa,OAAO,CAAC,mBAAmB;YAC9G,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;oBAChB,iBAAiB,CAAC,MAAM,EAAE,aAAa;gBACzC;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe;gBACf,YAAY;oBAAE,OAAO;oBAAI,QAAQ;oBAAI,UAAU;oBAAI,WAAW;oBAAI,aAAa;gBAAG;gBAClF;YACF,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,gBAAgB;QAE7B,IAAI;YACF,MAAM,cAAc,KAAK,GAAG,aAAa,OAAO,CAAC,kBAAkB,CAAC,EAAE,aAAa,OAAO,CAAC,mBAAmB;YAC9G,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,IAAI,EAAE;gBACxD,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,MAAM,EAAE,aAAa;gBACzC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf;YACF,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,IAAI,WAAW;QACb,qBAAO,8OAAC;YAAI,WAAU;sBAAmB;;;;;;IAC3C;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyB;;;;;;kCACvC,8OAAC;wBACC,SAAS,IAAM,eAAe;wBAC9B,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;;;;;;;YAKtC,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAK,UAAU;wBAAgB,WAAU;;0CACxC,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAClD,8OAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,YAAY;gDAAC,GAAG,QAAQ;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAA;wCAChE,WAAU;;;;;;;;;;;;0CAGd,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAClD,8OAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,OAAO,SAAS,MAAM;wCACtB,UAAU,CAAC,IAAM,YAAY;gDAAC,GAAG,QAAQ;gDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;4CAAA;wCACjE,WAAU;;;;;;;;;;;;0CAGd,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAClD,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO,SAAS,QAAQ;wCACxB,UAAU,CAAC,IAAM,YAAY;gDAAC,GAAG,QAAQ;gDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4CAAA;wCACnE,WAAU;;;;;;;;;;;;0CAGd,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAClD,8OAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,aAAY;wCACZ,OAAO,SAAS,SAAS;wCACzB,UAAU,CAAC,IAAM,YAAY;gDAAC,GAAG,QAAQ;gDAAE,WAAW,EAAE,MAAM,CAAC,KAAK;4CAAA;wCACpE,WAAU;;;;;;;;;;;;0CAGd,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAClD,8OAAC;wCACC,MAAM;wCACN,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,YAAY;gDAAC,GAAG,QAAQ;gDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4CAAA;wCACtE,WAAU;;;;;;;;;;;;0CAGd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,8OAAC;gBAAI,WAAU;;oBACZ,YAAY,GAAG,CAAC,CAAC,sBAChB,8OAAC;4BAAmB,WAAU;;8CAC5B,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAiB,MAAM,KAAK;;;;;;sDAC1C,8OAAC;4CAAE,WAAU;;gDAAwB;gDAAI,MAAM,MAAM;gDAAC;gDAAO,MAAM,QAAQ;;;;;;;wCAC1E,MAAM,WAAW,kBAChB,8OAAC;4CAAE,WAAU;sDAA8B,MAAM,WAAW;;;;;;;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS,IAAM,kBAAkB,MAAM,EAAE;wCACzC,WAAU;kDAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;;;;;;;;;;;;2BAbV,MAAM,EAAE;;;;;oBAmBnB,YAAY,MAAM,KAAK,mBACtB,8OAAC;wBAAI,WAAU;kCAAiC;;;;;;;;;;;;;;;;;;AAO1D;AAEA,SAAS;IACP,qBACE,8OAAC;;0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyB;;;;;;kCACvC,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;gCAAO,MAAM;;;;;;4BAAM;;;;;;;;;;;;;0BAKvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgB;;;;;;8CAC9B,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAEvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,2MAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;8CAEd,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5B;AAEA,SAAS;IACP,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAA8B;;;;;;0BAE5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC;gCACC,MAAK;gCACL,cAAa;gCACb,WAAU;;;;;;;;;;;;kCAId,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAClD,8OAAC;gCACC,MAAM;gCACN,cAAa;gCACb,WAAU;;;;;;;;;;;;kCAId,8OAAC;wBAAO,WAAU;kCAAgE;;;;;;;;;;;;;;;;;;AAM1F", "debugId": null}}, {"offset": {"line": 910, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/settings.js", "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/node_modules/lucide-react/src/icons/settings.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915',\n      key: '1i5ecw',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Settings\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOS42NzEgNC4xMzZhMi4zNCAyLjM0IDAgMCAxIDQuNjU5IDAgMi4zNCAyLjM0IDAgMCAwIDMuMzE5IDEuOTE1IDIuMzQgMi4zNCAwIDAgMSAyLjMzIDQuMDMzIDIuMzQgMi4zNCAwIDAgMCAwIDMuODMxIDIuMzQgMi4zNCAwIDAgMS0yLjMzIDQuMDMzIDIuMzQgMi4zNCAwIDAgMC0zLjMxOSAxLjkxNSAyLjM0IDIuMzQgMCAwIDEtNC42NTkgMCAyLjM0IDIuMzQgMCAwIDAtMy4zMi0xLjkxNSAyLjM0IDIuMzQgMCAwIDEtMi4zMy00LjAzMyAyLjM0IDIuMzQgMCAwIDAgMC0zLjgzMUEyLjM0IDIuMzQgMCAwIDEgNi4zNSA2LjA1MWEyLjM0IDIuMzQgMCAwIDAgMy4zMTktMS45MTUiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/settings\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Settings = createLucideIcon('settings', __iconNode);\n\nexport default Settings;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU;YAAE,CAAA,CAAA,CAAA,CAAI,IAAA,CAAA;YAAM,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAA,CAAK,QAAA;QAAA,CAAU;KAAA;CAC1D;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,QAAA,CAAA,CAAA,wKAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAA,AAAjB,CAAA,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/music.js", "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/node_modules/lucide-react/src/icons/music.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M9 18V5l12-2v13', key: '1jmyc2' }],\n  ['circle', { cx: '6', cy: '18', r: '3', key: 'fqmcym' }],\n  ['circle', { cx: '18', cy: '16', r: '3', key: '1hluhg' }],\n];\n\n/**\n * @component @name Music\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSAxOFY1bDEyLTJ2MTMiIC8+CiAgPGNpcmNsZSBjeD0iNiIgY3k9IjE4IiByPSIzIiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iMTYiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/music\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Music = createLucideIcon('music', __iconNode);\n\nexport default Music;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAmB,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAChD;QAAC,QAAA,CAAA;QAAU,CAAA;YAAE,EAAA,CAAA,CAAI,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAI,IAAA,CAAA;YAAM,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA;YAAK,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU;YAAE,CAAA,CAAA,CAAA,CAAI,IAAA,CAAA;YAAM,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAA,CAAK,QAAA;QAAA,CAAU;KAAA;CAC1D;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,wKAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/video.js", "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/node_modules/lucide-react/src/icons/video.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5',\n      key: 'ftymec',\n    },\n  ],\n  ['rect', { x: '2', y: '6', width: '14', height: '12', rx: '2', key: '158x01' }],\n];\n\n/**\n * @component @name Video\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTYgMTMgNS4yMjMgMy40ODJhLjUuNSAwIDAgMCAuNzc3LS40MTZWNy44N2EuNS41IDAgMCAwLS43NTItLjQzMkwxNiAxMC41IiAvPgogIDxyZWN0IHg9IjIiIHk9IjYiIHdpZHRoPSIxNCIgaGVpZ2h0PSIxMiIgcng9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/video\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Video = createLucideIcon('video', __iconNode);\n\nexport default Video;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,CAAA,CAAA,CAAG,CAAA,CAAA;YAAK,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAA,CAAA,GAAQ,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAChF;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,wKAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1053, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/user.js", "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA6C,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU;YAAE,CAAA,CAAA,CAAA,CAAI,IAAA,CAAA;YAAM,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA;YAAK,CAAA,CAAA,CAAA,CAAA,CAAK,QAAA;QAAA,CAAU;KAAA;CACzD;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,IAAA,CAAA,CAAA,wKAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1097, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/plus.js", "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC3C;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,IAAA,CAAA,CAAA,wKAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/square-pen.js", "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/node_modules/lucide-react/src/icons/square-pen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7', key: '1m0v6g' }],\n  [\n    'path',\n    {\n      d: 'M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z',\n      key: 'ohrbg2',\n    },\n  ],\n];\n\n/**\n * @component @name SquarePen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi03IiAvPgogIDxwYXRoIGQ9Ik0xOC4zNzUgMi42MjVhMSAxIDAgMCAxIDMgM2wtOS4wMTMgOS4wMTRhMiAyIDAgMCAxLS44NTMuNTA1bC0yLjg3My44NGEuNS41IDAgMCAxLS42Mi0uNjJsLjg0LTIuODczYTIgMiAwIDAgMSAuNTA2LS44NTJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquarePen = createLucideIcon('square-pen', __iconNode);\n\nexport default SquarePen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA8D,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IAC3F;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,SAAA,CAAA,CAAA,wKAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}, {"offset": {"line": 1181, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js", "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 11v6', key: 'nco0om' }],\n  ['path', { d: 'M14 11v6', key: 'outv1u' }],\n  ['path', { d: 'M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6', key: 'miytrc' }],\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2', key: 'e791ji' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTQgMTF2NiIgLz4KICA8cGF0aCBkPSJNMTkgNnYxNGEyIDIgMCAwIDEtMiAySDdhMiAyIDAgMCAxLTItMlY2IiAvPgogIDxwYXRoIGQ9Ik0zIDZoMTgiIC8+CiAgPHBhdGggZD0iTTggNlY0YTIgMiAwIDAgMSAyLTJoNGEyIDIgMCAwIDEgMiAydjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAY,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA4C,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAW,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAA0C,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzE;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,MAAA,CAAA,CAAA,wKAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}]}