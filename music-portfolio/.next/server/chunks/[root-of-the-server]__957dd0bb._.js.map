{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/bilibili/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\n// 从B站URL中提取视频ID\nfunction extractBilibiliId(url: string): string | null {\n  try {\n    // 支持多种B站URL格式\n    const patterns = [\n      /bilibili\\.com\\/video\\/(BV[a-zA-Z0-9]+)/,\n      /bilibili\\.com\\/video\\/(av\\d+)/,\n      /b23\\.tv\\/([a-zA-Z0-9]+)/,\n    ]\n\n    for (const pattern of patterns) {\n      const match = url.match(pattern)\n      if (match) {\n        return match[1]\n      }\n    }\n\n    return null\n  } catch (error) {\n    return null\n  }\n}\n\n// 从B站获取视频信息和缩略图\nasync function getBilibiliVideoInfo(bvid: string): Promise<{ title?: string; pic?: string; desc?: string } | null> {\n  try {\n    // 使用B站的公开API获取视频信息\n    const response = await fetch(`https://api.bilibili.com/x/web-interface/view?bvid=${bvid}`, {\n      headers: {\n        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',\n        'Referer': 'https://www.bilibili.com/'\n      }\n    })\n\n    if (!response.ok) {\n      return null\n    }\n\n    const data = await response.json()\n    \n    if (data.code === 0 && data.data) {\n      return {\n        title: data.data.title,\n        pic: data.data.pic,\n        desc: data.data.desc\n      }\n    }\n\n    return null\n  } catch (error) {\n    console.error('Error fetching Bilibili video info:', error)\n    return null\n  }\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const url = searchParams.get('url')\n\n    if (!url) {\n      return NextResponse.json(\n        { error: 'URL parameter is required' },\n        { status: 400 }\n      )\n    }\n\n    // 提取视频ID\n    const bvid = extractBilibiliId(url)\n    if (!bvid) {\n      return NextResponse.json(\n        { error: 'Invalid Bilibili URL' },\n        { status: 400 }\n      )\n    }\n\n    // 获取视频信息\n    const videoInfo = await getBilibiliVideoInfo(bvid)\n    if (!videoInfo) {\n      return NextResponse.json(\n        { error: 'Failed to fetch video information. The video may not exist or be private.' },\n        { status: 404 }\n      )\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        bvid,\n        title: videoInfo.title,\n        thumbnail: videoInfo.pic,\n        description: videoInfo.desc\n      }\n    })\n  } catch (error) {\n    console.error('Error in Bilibili API:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,gBAAgB;AAChB,SAAS,kBAAkB,GAAW;IACpC,IAAI;QACF,cAAc;QACd,MAAM,WAAW;YACf;YACA;YACA;SACD;QAED,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,QAAQ,IAAI,KAAK,CAAC;YACxB,IAAI,OAAO;gBACT,OAAO,KAAK,CAAC,EAAE;YACjB;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEA,gBAAgB;AAChB,eAAe,qBAAqB,IAAY;IAC9C,IAAI;QACF,mBAAmB;QACnB,MAAM,WAAW,MAAM,MAAM,CAAC,mDAAmD,EAAE,MAAM,EAAE;YACzF,SAAS;gBACP,cAAc;gBACd,WAAW;YACb;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,OAAO;QACT;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,EAAE;YAChC,OAAO;gBACL,OAAO,KAAK,IAAI,CAAC,KAAK;gBACtB,KAAK,KAAK,IAAI,CAAC,GAAG;gBAClB,MAAM,KAAK,IAAI,CAAC,IAAI;YACtB;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;QAE7B,IAAI,CAAC,KAAK;YACR,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,OAAO,kBAAkB;QAC/B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,YAAY,MAAM,qBAAqB;QAC7C,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4E,GACrF;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA,OAAO,UAAU,KAAK;gBACtB,WAAW,UAAU,GAAG;gBACxB,aAAa,UAAU,IAAI;YAC7B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}