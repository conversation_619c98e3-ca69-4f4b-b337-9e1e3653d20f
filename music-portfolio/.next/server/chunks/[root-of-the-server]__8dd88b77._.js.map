{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\n\nexport interface AdminSession {\n  isAuthenticated: boolean\n  username?: string\n}\n\nexport function checkAdminAuth(request: NextRequest): AdminSession {\n  const authHeader = request.headers.get('authorization')\n  \n  if (!authHeader || !authHeader.startsWith('Basic ')) {\n    return { isAuthenticated: false }\n  }\n\n  try {\n    const base64Credentials = authHeader.slice(6)\n    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii')\n    const [username, password] = credentials.split(':')\n\n    const adminUsername = process.env.ADMIN_USERNAME || 'admin'\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'\n\n    if (username === adminUsername && password === adminPassword) {\n      return { isAuthenticated: true, username }\n    }\n\n    return { isAuthenticated: false }\n  } catch (error) {\n    return { isAuthenticated: false }\n  }\n}\n\nexport function requireAuth() {\n  return new Response('Unauthorized', {\n    status: 401,\n    headers: {\n      'WWW-Authenticate': 'Basic realm=\"Admin Area\"',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAOO,SAAS,eAAe,OAAoB;IACjD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,WAAW;QACnD,OAAO;YAAE,iBAAiB;QAAM;IAClC;IAEA,IAAI;QACF,MAAM,oBAAoB,WAAW,KAAK,CAAC;QAC3C,MAAM,cAAc,OAAO,IAAI,CAAC,mBAAmB,UAAU,QAAQ,CAAC;QACtE,MAAM,CAAC,UAAU,SAAS,GAAG,YAAY,KAAK,CAAC;QAE/C,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QACpD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEpD,IAAI,aAAa,iBAAiB,aAAa,eAAe;YAC5D,OAAO;gBAAE,iBAAiB;gBAAM;YAAS;QAC3C;QAEA,OAAO;YAAE,iBAAiB;QAAM;IAClC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,iBAAiB;QAAM;IAClC;AACF;AAEO,SAAS;IACd,OAAO,IAAI,SAAS,gBAAgB;QAClC,QAAQ;QACR,SAAS;YACP,oBAAoB;QACtB;IACF;AACF", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/admin/music/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { checkAdminAuth, requireAuth } from '@/lib/auth'\n\n// 从B站URL中提取视频ID\nfunction extractBilibiliId(url: string): string | null {\n  try {\n    // 支持多种B站URL格式\n    const patterns = [\n      /bilibili\\.com\\/video\\/(BV[a-zA-Z0-9]+)/,\n      /bilibili\\.com\\/video\\/(av\\d+)/,\n      /b23\\.tv\\/([a-zA-Z0-9]+)/,\n    ]\n\n    for (const pattern of patterns) {\n      const match = url.match(pattern)\n      if (match) {\n        return match[1]\n      }\n    }\n\n    return null\n  } catch (error) {\n    return null\n  }\n}\n\n// 模拟数据存储 - 实际项目中应该使用数据库\nlet musicVideos = [\n  {\n    id: '1',\n    title: '《夜空中最亮的星》原创音乐MV',\n    artist: 'KIMAHALA',\n    description: '我的第一首原创歌曲，献给所有在黑暗中寻找光明的人。这首歌记录了我对音乐的初心和对梦想的坚持。',\n    bilibili_url: 'https://www.bilibili.com/video/BV1234567890',\n    bilibili_embed_id: 'BV1234567890',\n    thumbnail_url: '/thumbnails/music1.jpg',\n    created_at: '2024-01-15T00:00:00Z',\n    updated_at: '2024-01-15T00:00:00Z',\n  },\n  {\n    id: '2',\n    title: '《时光倒流》吉他弹唱版',\n    artist: 'KIMAHALA',\n    description: '用最简单的吉他和歌声，诉说关于时光和回忆的故事。希望这首歌能唤起你心中最美好的回忆。',\n    bilibili_url: 'https://www.bilibili.com/video/BV2345678901',\n    bilibili_embed_id: 'BV2345678901',\n    thumbnail_url: '/thumbnails/music2.jpg',\n    created_at: '2024-01-12T00:00:00Z',\n    updated_at: '2024-01-12T00:00:00Z',\n  },\n  {\n    id: '3',\n    title: '《远方的路》创作过程分享',\n    artist: 'KIMAHALA',\n    description: '分享这首摇滚歌曲的创作过程，从灵感迸发到最终完成的全过程记录。包含编曲、录音等幕后花絮。',\n    bilibili_url: 'https://www.bilibili.com/video/BV3456789012',\n    bilibili_embed_id: 'BV3456789012',\n    thumbnail_url: '/thumbnails/music3.jpg',\n    created_at: '2024-01-10T00:00:00Z',\n    updated_at: '2024-01-10T00:00:00Z',\n  },\n]\n\nexport async function GET(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  return NextResponse.json({ success: true, data: musicVideos })\n}\n\nexport async function POST(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n\n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  try {\n    const body = await request.json()\n    const { title, artist, description, bilibili_url, thumbnail_url } = body\n\n    if (!title || !artist || !bilibili_url) {\n      return NextResponse.json(\n        { error: 'Title, artist, and bilibili_url are required' },\n        { status: 400 }\n      )\n    }\n\n    // 从B站URL中提取embed ID\n    const bilibiliEmbedId = extractBilibiliId(bilibili_url)\n    if (!bilibiliEmbedId) {\n      return NextResponse.json(\n        { error: 'Invalid Bilibili URL' },\n        { status: 400 }\n      )\n    }\n\n    const newVideo = {\n      id: Date.now().toString(),\n      title,\n      artist,\n      description: description || '',\n      bilibili_url,\n      bilibili_embed_id: bilibiliEmbedId,\n      thumbnail_url: thumbnail_url || '',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    }\n\n    musicVideos.push(newVideo)\n\n    return NextResponse.json({ success: true, data: newVideo })\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Invalid request body' },\n      { status: 400 }\n    )\n  }\n}\n\n// 更新音乐视频\nexport async function PUT(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n\n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  try {\n    const body = await request.json()\n    const { id, title, artist, description, bilibili_url, thumbnail_url } = body\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'Video ID is required' },\n        { status: 400 }\n      )\n    }\n\n    const videoIndex = musicVideos.findIndex(video => video.id === id)\n    if (videoIndex === -1) {\n      return NextResponse.json(\n        { error: 'Video not found' },\n        { status: 404 }\n      )\n    }\n\n    if (!title || !artist || !bilibili_url) {\n      return NextResponse.json(\n        { error: 'Title, artist, and bilibili_url are required' },\n        { status: 400 }\n      )\n    }\n\n    // 从B站URL中提取embed ID\n    const bilibiliEmbedId = extractBilibiliId(bilibili_url)\n    if (!bilibiliEmbedId) {\n      return NextResponse.json(\n        { error: 'Invalid Bilibili URL' },\n        { status: 400 }\n      )\n    }\n\n    const updatedVideo = {\n      ...musicVideos[videoIndex],\n      title,\n      artist,\n      description: description || '',\n      bilibili_url,\n      bilibili_embed_id: bilibiliEmbedId,\n      thumbnail_url: thumbnail_url || '',\n      updated_at: new Date().toISOString(),\n    }\n\n    musicVideos[videoIndex] = updatedVideo\n\n    return NextResponse.json({ success: true, data: updatedVideo })\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Invalid request body' },\n      { status: 400 }\n    )\n  }\n}\n\nexport async function DELETE(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  try {\n    const { searchParams } = new URL(request.url)\n    const id = searchParams.get('id')\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'ID is required' },\n        { status: 400 }\n      )\n    }\n\n    const index = musicVideos.findIndex(video => video.id === id)\n\n    if (index === -1) {\n      return NextResponse.json(\n        { error: 'Video not found' },\n        { status: 404 }\n      )\n    }\n\n    musicVideos.splice(index, 1)\n\n    return NextResponse.json({ success: true })\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Failed to delete track' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,gBAAgB;AAChB,SAAS,kBAAkB,GAAW;IACpC,IAAI;QACF,cAAc;QACd,MAAM,WAAW;YACf;YACA;YACA;SACD;QAED,KAAK,MAAM,WAAW,SAAU;YAC9B,MAAM,QAAQ,IAAI,KAAK,CAAC;YACxB,IAAI,OAAO;gBACT,OAAO,KAAK,CAAC,EAAE;YACjB;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEA,wBAAwB;AACxB,IAAI,cAAc;IAChB;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,YAAY;QACZ,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,YAAY;QACZ,YAAY;IACd;CACD;AAEM,eAAe,IAAI,OAAoB;IAC5C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;QAAM,MAAM;IAAY;AAC9D;AAEO,eAAe,KAAK,OAAoB;IAC7C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;QAEpE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,MAAM,kBAAkB,kBAAkB;QAC1C,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW;YACf,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA;YACA,aAAa,eAAe;YAC5B;YACA,mBAAmB;YACnB,eAAe,iBAAiB;YAChC,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,YAAY,IAAI,CAAC;QAEjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM,MAAM;QAAS;IAC3D,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;QAExE,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,aAAa,YAAY,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAC/D,IAAI,eAAe,CAAC,GAAG;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,MAAM,kBAAkB,kBAAkB;QAC1C,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,eAAe;YACnB,GAAG,WAAW,CAAC,WAAW;YAC1B;YACA;YACA,aAAa,eAAe;YAC5B;YACA,mBAAmB;YACnB,eAAe,iBAAiB;YAChC,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,WAAW,CAAC,WAAW,GAAG;QAE1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM,MAAM;QAAa;IAC/D,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,OAAO,OAAoB;IAC/C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;QAE5B,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiB,GAC1B;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,YAAY,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAE1D,IAAI,UAAU,CAAC,GAAG;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,YAAY,MAAM,CAAC,OAAO;QAE1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAK;IAC3C,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}