{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/music/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\n// 这里应该从数据库获取数据，现在使用模拟数据\nconst musicVideos = [\n  {\n    id: '1',\n    title: '《夜空中最亮的星》原创音乐MV',\n    artist: 'KIMAHALA',\n    description: '我的第一首原创歌曲，献给所有在黑暗中寻找光明的人。这首歌记录了我对音乐的初心和对梦想的坚持。',\n    bilibili_url: 'https://www.bilibili.com/video/BV1234567890',\n    bilibili_embed_id: 'BV1234567890',\n    thumbnail_url: '/thumbnails/music1.jpg',\n    created_at: '2024-01-15',\n  },\n  {\n    id: '2',\n    title: '《时光倒流》吉他弹唱版',\n    artist: 'KIMAHALA',\n    description: '用最简单的吉他和歌声，诉说关于时光和回忆的故事。希望这首歌能唤起你心中最美好的回忆。',\n    bilibili_url: 'https://www.bilibili.com/video/BV2345678901',\n    bilibili_embed_id: 'BV2345678901',\n    thumbnail_url: '/thumbnails/music2.jpg',\n    created_at: '2024-01-12',\n  },\n  {\n    id: '3',\n    title: '《远方的路》创作过程分享',\n    artist: 'KIMAHALA',\n    description: '分享这首摇滚歌曲的创作过程，从灵感迸发到最终完成的全过程记录。包含编曲、录音等幕后花絮。',\n    bilibili_url: 'https://www.bilibili.com/video/BV3456789012',\n    bilibili_embed_id: 'BV3456789012',\n    thumbnail_url: '/thumbnails/music3.jpg',\n    created_at: '2024-01-10',\n  },\n  {\n    id: '4',\n    title: '《青春无悔》现场演出版',\n    artist: 'KIMAHALA',\n    description: '在小型音乐节上的现场演出，感受最真实的音乐力量。这是我和观众最直接的音乐交流。',\n    bilibili_url: 'https://www.bilibili.com/video/BV4567890123',\n    bilibili_embed_id: 'BV4567890123',\n    thumbnail_url: '/thumbnails/music4.jpg',\n    created_at: '2024-01-08',\n  },\n  {\n    id: '5',\n    title: '翻唱《平凡之路》',\n    artist: 'KIMAHALA',\n    description: '用我的方式重新诠释朴树的经典作品《平凡之路》，加入了一些个人的理解和情感表达。',\n    bilibili_url: 'https://www.bilibili.com/video/BV5678901234',\n    bilibili_embed_id: 'BV5678901234',\n    thumbnail_url: '/thumbnails/music5.jpg',\n    created_at: '2024-01-05',\n  },\n]\n\nexport async function GET() {\n  return NextResponse.json({ success: true, data: musicVideos })\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,wBAAwB;AACxB,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,YAAY;IACd;IACA;QACE,IAAI;QACJ,OAAO;QACP,QAAQ;QACR,aAAa;QACb,cAAc;QACd,mBAAmB;QACnB,eAAe;QACf,YAAY;IACd;CACD;AAEM,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;QAAM,MAAM;IAAY;AAC9D", "debugId": null}}]}