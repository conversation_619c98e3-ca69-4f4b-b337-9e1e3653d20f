{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/lib/data-store.ts"], "sourcesContent": ["// 共享数据存储模块\n// 在实际应用中，这应该连接到真实的数据库\n\n// 使用全局变量确保数据在开发环境中保持一致\ndeclare global {\n  var __musicVideos: any[] | undefined\n  var __movies: any[] | undefined\n}\n\n// 音乐视频数据\nif (!global.__musicVideos) {\n  global.__musicVideos = []\n}\n\n// 获取音乐视频数据\nfunction getMusicVideosData() {\n  return global.__musicVideos || []\n}\n\n// 电影数据初始化\nif (!global.__movies) {\n  global.__movies = [\n    {\n      id: '1',\n      title: '教父',\n      description: '一个黑手党家族的传奇故事，展现了权力、家族和忠诚的复杂关系。弗朗西斯·福特·科波拉执导的经典之作，被誉为电影史上最伟大的作品之一。',\n      poster_url: '/posters/godfather.jpg',\n      movie_link: 'https://example.com/godfather',\n      category: 'gangster',\n      rating: 9.3,\n      release_year: 1972,\n      created_at: '2024-01-15T00:00:00Z',\n      updated_at: '2024-01-15T00:00:00Z',\n    },\n  {\n    id: '2',\n    title: '肖申克的救赎',\n    description: '一个关于希望、友谊和救赎的感人故事。安迪在监狱中的经历告诉我们，即使在最黑暗的地方，希望之光也永远不会熄灭。',\n    poster_url: '/posters/shawshank.jpg',\n    movie_link: 'https://example.com/shawshank',\n    category: 'drama',\n    rating: 9.7,\n    release_year: 1994,\n    created_at: '2024-01-14T00:00:00Z',\n    updated_at: '2024-01-14T00:00:00Z',\n  },\n  {\n    id: '3',\n    title: '泰坦尼克号',\n    description: '一个跨越阶级的爱情故事，在历史上最著名的海难背景下展开。杰克和露丝的爱情故事感动了全世界观众。',\n    poster_url: '/posters/titanic.jpg',\n    movie_link: 'https://example.com/titanic',\n    category: 'romance',\n    rating: 7.9,\n    release_year: 1997,\n    created_at: '2024-01-13T00:00:00Z',\n    updated_at: '2024-01-13T00:00:00Z',\n  },\n  {\n    id: '4',\n    title: '辛德勒的名单',\n    description: '二战期间，德国商人奥斯卡·辛德勒拯救犹太人的真实故事。这部黑白电影以其深刻的人道主义精神震撼人心。',\n    poster_url: '/posters/schindler.jpg',\n    movie_link: 'https://example.com/schindler',\n    category: 'history',\n    rating: 9.0,\n    release_year: 1993,\n    created_at: '2024-01-12T00:00:00Z',\n    updated_at: '2024-01-12T00:00:00Z',\n  },\n  {\n    id: '5',\n    title: '好家伙',\n    description: '基于真实事件改编的黑帮电影，讲述了亨利·希尔在黑手党中的生活经历。马丁·斯科塞斯的又一经典之作。',\n    poster_url: '/posters/goodfellas.jpg',\n    movie_link: 'https://example.com/goodfellas',\n    category: 'gangster',\n    rating: 8.7,\n    release_year: 1990,\n    created_at: '2024-01-11T00:00:00Z',\n    updated_at: '2024-01-11T00:00:00Z',\n  },\n  {\n    id: '6',\n    title: '沉默的羔羊',\n    description: '一部心理惊悚片的经典之作，FBI学员克拉丽丝与食人魔汉尼拔博士的心理较量令人毛骨悚然。',\n    poster_url: '/posters/silence.jpg',\n    movie_link: 'https://example.com/silence',\n    category: 'thriller',\n    rating: 8.6,\n    release_year: 1991,\n    created_at: '2024-01-10T00:00:00Z',\n    updated_at: '2024-01-10T00:00:00Z',\n  },\n  {\n    id: '7',\n    title: '阿甘正传',\n    description: '一个智商不高但心地善良的男人的人生传奇。阿甘用他的纯真和坚持诠释了什么是真正的成功。',\n    poster_url: '/posters/forrest.jpg',\n    movie_link: 'https://example.com/forrest',\n    category: 'drama',\n    rating: 8.8,\n    release_year: 1994,\n    created_at: '2024-01-09T00:00:00Z',\n    updated_at: '2024-01-09T00:00:00Z',\n  },\n  {\n    id: '8',\n    title: '勇敢的心',\n    description: '苏格兰民族英雄威廉·华莱士为自由而战的史诗故事。梅尔·吉布森自导自演的历史巨作。',\n    poster_url: '/posters/braveheart.jpg',\n    movie_link: 'https://example.com/braveheart',\n    category: 'history',\n    rating: 8.4,\n    release_year: 1995,\n    created_at: '2024-01-08T00:00:00Z',\n    updated_at: '2024-01-08T00:00:00Z',\n  },\n  {\n    id: '9',\n    title: '终结者2：审判日',\n    description: '科幻动作片的里程碑之作，阿诺·施瓦辛格饰演的终结者成为了电影史上最经典的角色之一。',\n    poster_url: '/posters/terminator2.jpg',\n    movie_link: 'https://example.com/terminator2',\n    category: 'action',\n    rating: 8.5,\n    release_year: 1991,\n    created_at: '2024-01-07T00:00:00Z',\n    updated_at: '2024-01-07T00:00:00Z',\n  },\n  {\n    id: '10',\n    title: '罗马假日',\n    description: '奥黛丽·赫本和格利高里·派克主演的经典爱情喜剧，讲述了公主与记者在罗马的浪漫邂逅。',\n    poster_url: '/posters/roman.jpg',\n    movie_link: 'https://example.com/roman',\n    category: 'romance',\n    rating: 8.1,\n    release_year: 1953,\n    created_at: '2024-01-06T00:00:00Z',\n    updated_at: '2024-01-06T00:00:00Z',\n  },\n  ]\n}\n\n// 获取电影数据\nfunction getMoviesData() {\n  return global.__movies || []\n}\n\n// 音乐视频操作函数\nexport function getMusicVideos() {\n  return [...getMusicVideosData()]\n}\n\nexport function addMusicVideo(video: any) {\n  const videos = getMusicVideosData()\n  videos.push(video)\n  global.__musicVideos = videos\n}\n\nexport function updateMusicVideo(id: string, updatedVideo: any) {\n  const videos = getMusicVideosData()\n  const index = videos.findIndex(v => v.id === id)\n  if (index !== -1) {\n    videos[index] = updatedVideo\n    global.__musicVideos = videos\n  }\n}\n\nexport function deleteMusicVideo(id: string) {\n  const videos = getMusicVideosData()\n  const index = videos.findIndex(v => v.id === id)\n  if (index !== -1) {\n    videos.splice(index, 1)\n    global.__musicVideos = videos\n  }\n}\n\n// 电影操作函数\nexport function getMovies() {\n  return [...getMoviesData()]\n}\n\nexport function addMovie(movie: any) {\n  const movies = getMoviesData()\n  movies.push(movie)\n  global.__movies = movies\n}\n\nexport function updateMovie(id: string, updatedMovie: any) {\n  const movies = getMoviesData()\n  const index = movies.findIndex(m => m.id === id)\n  if (index !== -1) {\n    movies[index] = updatedMovie\n    global.__movies = movies\n  }\n}\n\nexport function deleteMovie(id: string) {\n  const movies = getMoviesData()\n  const index = movies.findIndex(m => m.id === id)\n  if (index !== -1) {\n    movies.splice(index, 1)\n    global.__movies = movies\n  }\n}\n"], "names": [], "mappings": "AAAA,WAAW;AACX,sBAAsB;AAEtB,uBAAuB;;;;;;;;;;;AAMvB,SAAS;AACT,IAAI,CAAC,OAAO,aAAa,EAAE;IACzB,OAAO,aAAa,GAAG,EAAE;AAC3B;AAEA,WAAW;AACX,SAAS;IACP,OAAO,OAAO,aAAa,IAAI,EAAE;AACnC;AAEA,UAAU;AACV,IAAI,CAAC,OAAO,QAAQ,EAAE;IACpB,OAAO,QAAQ,GAAG;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;QACd;QACF;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,cAAc;YACd,YAAY;YACZ,YAAY;QACd;KACC;AACH;AAEA,SAAS;AACT,SAAS;IACP,OAAO,OAAO,QAAQ,IAAI,EAAE;AAC9B;AAGO,SAAS;IACd,OAAO;WAAI;KAAqB;AAClC;AAEO,SAAS,cAAc,KAAU;IACtC,MAAM,SAAS;IACf,OAAO,IAAI,CAAC;IACZ,OAAO,aAAa,GAAG;AACzB;AAEO,SAAS,iBAAiB,EAAU,EAAE,YAAiB;IAC5D,MAAM,SAAS;IACf,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC7C,IAAI,UAAU,CAAC,GAAG;QAChB,MAAM,CAAC,MAAM,GAAG;QAChB,OAAO,aAAa,GAAG;IACzB;AACF;AAEO,SAAS,iBAAiB,EAAU;IACzC,MAAM,SAAS;IACf,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC7C,IAAI,UAAU,CAAC,GAAG;QAChB,OAAO,MAAM,CAAC,OAAO;QACrB,OAAO,aAAa,GAAG;IACzB;AACF;AAGO,SAAS;IACd,OAAO;WAAI;KAAgB;AAC7B;AAEO,SAAS,SAAS,KAAU;IACjC,MAAM,SAAS;IACf,OAAO,IAAI,CAAC;IACZ,OAAO,QAAQ,GAAG;AACpB;AAEO,SAAS,YAAY,EAAU,EAAE,YAAiB;IACvD,MAAM,SAAS;IACf,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC7C,IAAI,UAAU,CAAC,GAAG;QAChB,MAAM,CAAC,MAAM,GAAG;QAChB,OAAO,QAAQ,GAAG;IACpB;AACF;AAEO,SAAS,YAAY,EAAU;IACpC,MAAM,SAAS;IACf,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC7C,IAAI,UAAU,CAAC,GAAG;QAChB,OAAO,MAAM,CAAC,OAAO;QACrB,OAAO,QAAQ,GAAG;IACpB;AACF", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/movies/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport { getMovies } from '@/lib/data-store'\n\nexport async function GET() {\n  try {\n    const movies = getMovies()\n    return NextResponse.json({ success: true, data: movies })\n  } catch (error) {\n    console.error('Error fetching movies:', error)\n    return NextResponse.json({ success: false, data: [] })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,SAAS,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD;QACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM,MAAM;QAAO;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAO,MAAM,EAAE;QAAC;IACtD;AACF", "debugId": null}}]}