{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\n\nexport interface AdminSession {\n  isAuthenticated: boolean\n  username?: string\n}\n\nexport function checkAdminAuth(request: NextRequest): AdminSession {\n  const authHeader = request.headers.get('authorization')\n  \n  if (!authHeader || !authHeader.startsWith('Basic ')) {\n    return { isAuthenticated: false }\n  }\n\n  try {\n    const base64Credentials = authHeader.slice(6)\n    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii')\n    const [username, password] = credentials.split(':')\n\n    const adminUsername = process.env.ADMIN_USERNAME || 'admin'\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'\n\n    if (username === adminUsername && password === adminPassword) {\n      return { isAuthenticated: true, username }\n    }\n\n    return { isAuthenticated: false }\n  } catch (error) {\n    return { isAuthenticated: false }\n  }\n}\n\nexport function requireAuth() {\n  return new Response('Unauthorized', {\n    status: 401,\n    headers: {\n      'WWW-Authenticate': 'Basic realm=\"Admin Area\"',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAOO,SAAS,eAAe,OAAoB;IACjD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,WAAW;QACnD,OAAO;YAAE,iBAAiB;QAAM;IAClC;IAEA,IAAI;QACF,MAAM,oBAAoB,WAAW,KAAK,CAAC;QAC3C,MAAM,cAAc,OAAO,IAAI,CAAC,mBAAmB,UAAU,QAAQ,CAAC;QACtE,MAAM,CAAC,UAAU,SAAS,GAAG,YAAY,KAAK,CAAC;QAE/C,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QACpD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEpD,IAAI,aAAa,iBAAiB,aAAa,eAAe;YAC5D,OAAO;gBAAE,iBAAiB;gBAAM;YAAS;QAC3C;QAEA,OAAO;YAAE,iBAAiB;QAAM;IAClC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,iBAAiB;QAAM;IAClC;AACF;AAEO,SAAS;IACd,OAAO,IAAI,SAAS,gBAAgB;QAClC,QAAQ;QACR,SAAS;YACP,oBAAoB;QACtB;IACF;AACF", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/upload/video/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { writeFile, mkdir } from 'fs/promises'\nimport { existsSync } from 'fs'\nimport path from 'path'\nimport { checkAdminAuth, requireAuth } from '@/lib/auth'\n\n// 支持的视频格式\nconst ALLOWED_VIDEO_TYPES = [\n  'video/mp4',\n  'video/quicktime', // .mov\n  'video/x-msvideo', // .avi\n  'video/webm',\n  'video/x-ms-wmv', // .wmv\n  'video/x-flv', // .flv\n]\n\n// 文件大小限制 (10GB)\nconst MAX_FILE_SIZE = 10 * 1024 * 1024 * 1024\n\nexport async function POST(request: NextRequest) {\n  try {\n    // 检查管理员权限\n    const auth = checkAdminAuth(request)\n    if (!auth.isAuthenticated) {\n      return requireAuth()\n    }\n\n    const formData = await request.formData()\n    const file = formData.get('video') as File\n\n    if (!file) {\n      return NextResponse.json(\n        { error: 'No video file provided' },\n        { status: 400 }\n      )\n    }\n\n    // 验证文件类型\n    if (!ALLOWED_VIDEO_TYPES.includes(file.type)) {\n      return NextResponse.json(\n        { error: `Unsupported file type. Allowed types: ${ALLOWED_VIDEO_TYPES.join(', ')}` },\n        { status: 400 }\n      )\n    }\n\n    // 验证文件大小\n    if (file.size > MAX_FILE_SIZE) {\n      return NextResponse.json(\n        { error: `File too large. Maximum size: ${MAX_FILE_SIZE / (1024 * 1024 * 1024)}GB` },\n        { status: 400 }\n      )\n    }\n\n    // 创建上传目录\n    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'movies')\n    if (!existsSync(uploadDir)) {\n      await mkdir(uploadDir, { recursive: true })\n    }\n\n    // 生成唯一文件名\n    const timestamp = Date.now()\n    const fileExtension = path.extname(file.name)\n    const fileName = `movie_${timestamp}${fileExtension}`\n    const filePath = path.join(uploadDir, fileName)\n\n    // 保存文件\n    const bytes = await file.arrayBuffer()\n    const buffer = Buffer.from(bytes)\n    await writeFile(filePath, buffer)\n\n    // 返回相对路径（用于数据库存储）\n    const relativePath = `/uploads/movies/${fileName}`\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        fileName,\n        filePath: relativePath,\n        fileSize: file.size,\n        fileType: file.type\n      }\n    })\n\n  } catch (error) {\n    console.error('Error uploading video:', error)\n    return NextResponse.json(\n      { error: 'Failed to upload video file' },\n      { status: 500 }\n    )\n  }\n}\n\n// 获取上传进度的辅助端点\nexport async function GET(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  return NextResponse.json({\n    success: true,\n    maxFileSize: MAX_FILE_SIZE,\n    allowedTypes: ALLOWED_VIDEO_TYPES\n  })\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,UAAU;AACV,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;IACA;CACD;AAED,gBAAgB;AAChB,MAAM,gBAAgB,KAAK,OAAO,OAAO;AAElC,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,UAAU;QACV,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;QAC5B,IAAI,CAAC,KAAK,eAAe,EAAE;YACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;QACnB;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,IAAI,CAAC,oBAAoB,QAAQ,CAAC,KAAK,IAAI,GAAG;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,sCAAsC,EAAE,oBAAoB,IAAI,CAAC,OAAO;YAAC,GACnF;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,IAAI,KAAK,IAAI,GAAG,eAAe;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO,CAAC,8BAA8B,EAAE,gBAAgB,CAAC,OAAO,OAAO,IAAI,EAAE,EAAE,CAAC;YAAC,GACnF;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,UAAU,WAAW;QAChE,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,YAAY;YAC1B,MAAM,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE,WAAW;gBAAE,WAAW;YAAK;QAC3C;QAEA,UAAU;QACV,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,gBAAgB,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,KAAK,IAAI;QAC5C,MAAM,WAAW,CAAC,MAAM,EAAE,YAAY,eAAe;QACrD,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;QAEtC,OAAO;QACP,MAAM,QAAQ,MAAM,KAAK,WAAW;QACpC,MAAM,SAAS,OAAO,IAAI,CAAC;QAC3B,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QAE1B,kBAAkB;QAClB,MAAM,eAAe,CAAC,gBAAgB,EAAE,UAAU;QAElD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA,UAAU;gBACV,UAAU,KAAK,IAAI;gBACnB,UAAU,KAAK,IAAI;YACrB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8B,GACvC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAC5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,aAAa;QACb,cAAc;IAChB;AACF", "debugId": null}}]}