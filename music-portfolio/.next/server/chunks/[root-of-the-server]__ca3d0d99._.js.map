{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/lib/auth.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\n\nexport interface AdminSession {\n  isAuthenticated: boolean\n  username?: string\n}\n\nexport function checkAdminAuth(request: NextRequest): AdminSession {\n  const authHeader = request.headers.get('authorization')\n  \n  if (!authHeader || !authHeader.startsWith('Basic ')) {\n    return { isAuthenticated: false }\n  }\n\n  try {\n    const base64Credentials = authHeader.slice(6)\n    const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii')\n    const [username, password] = credentials.split(':')\n\n    const adminUsername = process.env.ADMIN_USERNAME || 'admin'\n    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123'\n\n    if (username === adminUsername && password === adminPassword) {\n      return { isAuthenticated: true, username }\n    }\n\n    return { isAuthenticated: false }\n  } catch (error) {\n    return { isAuthenticated: false }\n  }\n}\n\nexport function requireAuth() {\n  return new Response('Unauthorized', {\n    status: 401,\n    headers: {\n      'WWW-Authenticate': 'Basic realm=\"Admin Area\"',\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAOO,SAAS,eAAe,OAAoB;IACjD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,WAAW;QACnD,OAAO;YAAE,iBAAiB;QAAM;IAClC;IAEA,IAAI;QACF,MAAM,oBAAoB,WAAW,KAAK,CAAC;QAC3C,MAAM,cAAc,OAAO,IAAI,CAAC,mBAAmB,UAAU,QAAQ,CAAC;QACtE,MAAM,CAAC,UAAU,SAAS,GAAG,YAAY,KAAK,CAAC;QAE/C,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QACpD,MAAM,gBAAgB,QAAQ,GAAG,CAAC,cAAc,IAAI;QAEpD,IAAI,aAAa,iBAAiB,aAAa,eAAe;YAC5D,OAAO;gBAAE,iBAAiB;gBAAM;YAAS;QAC3C;QAEA,OAAO;YAAE,iBAAiB;QAAM;IAClC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,iBAAiB;QAAM;IAClC;AACF;AAEO,SAAS;IACd,OAAO,IAAI,SAAS,gBAAgB;QAClC,QAAQ;QACR,SAAS;YACP,oBAAoB;QACtB;IACF;AACF", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/lib/simple-store.ts"], "sourcesContent": ["// 简单的文件数据存储\nimport { writeFileSync, readFileSync, existsSync } from 'fs'\nimport { join } from 'path'\n\nconst DATA_DIR = join(process.cwd(), 'data')\nconst MUSIC_FILE = join(DATA_DIR, 'music.json')\nconst MOVIES_FILE = join(DATA_DIR, 'movies.json')\n\n// 确保数据目录存在\nfunction ensureDataDir() {\n  try {\n    const fs = require('fs')\n    if (!fs.existsSync(DATA_DIR)) {\n      fs.mkdirSync(DATA_DIR, { recursive: true })\n    }\n  } catch (error) {\n    console.error('Error creating data directory:', error)\n  }\n}\n\n// 初始化数据文件\nfunction initializeData() {\n  ensureDataDir()\n  \n  // 初始化音乐数据（空数组）\n  if (!existsSync(MUSIC_FILE)) {\n    writeFileSync(MUSIC_FILE, JSON.stringify([], null, 2))\n  }\n  \n  // 初始化电影数据\n  if (!existsSync(MOVIES_FILE)) {\n    const initialMovies = [\n      {\n        id: '1',\n        title: '教父',\n        description: '一个黑手党家族的传奇故事，展现了权力、家族和忠诚的复杂关系。弗朗西斯·福特·科波拉执导的经典之作，被誉为电影史上最伟大的作品之一。',\n        poster_url: '/posters/godfather.jpg',\n        movie_link: 'https://example.com/godfather',\n        category: 'gangster',\n        rating: 9.3,\n        release_year: 1972,\n        created_at: '2024-01-15T00:00:00Z',\n        updated_at: '2024-01-15T00:00:00Z',\n      },\n      {\n        id: '2',\n        title: '肖申克的救赎',\n        description: '一个关于希望、友谊和救赎的感人故事。安迪在监狱中的经历告诉我们，即使在最黑暗的地方，希望之光也永远不会熄灭。',\n        poster_url: '/posters/shawshank.jpg',\n        movie_link: 'https://example.com/shawshank',\n        category: 'drama',\n        rating: 9.7,\n        release_year: 1994,\n        created_at: '2024-01-14T00:00:00Z',\n        updated_at: '2024-01-14T00:00:00Z',\n      },\n      {\n        id: '3',\n        title: '泰坦尼克号',\n        description: '一个跨越阶级的爱情故事，在历史上最著名的海难背景下展开。杰克和露丝的爱情故事感动了全世界观众。',\n        poster_url: '/posters/titanic.jpg',\n        movie_link: 'https://example.com/titanic',\n        category: 'romance',\n        rating: 7.9,\n        release_year: 1997,\n        created_at: '2024-01-13T00:00:00Z',\n        updated_at: '2024-01-13T00:00:00Z',\n      },\n      {\n        id: '4',\n        title: '辛德勒的名单',\n        description: '二战期间，德国商人奥斯卡·辛德勒拯救犹太人的真实故事。这部黑白电影以其深刻的人道主义精神震撼人心。',\n        poster_url: '/posters/schindler.jpg',\n        movie_link: 'https://example.com/schindler',\n        category: 'history',\n        rating: 9.0,\n        release_year: 1993,\n        created_at: '2024-01-12T00:00:00Z',\n        updated_at: '2024-01-12T00:00:00Z',\n      },\n      {\n        id: '5',\n        title: '好家伙',\n        description: '基于真实事件改编的黑帮电影，讲述了亨利·希尔在黑手党中的生活经历。马丁·斯科塞斯的又一经典之作。',\n        poster_url: '/posters/goodfellas.jpg',\n        movie_link: 'https://example.com/goodfellas',\n        category: 'gangster',\n        rating: 8.7,\n        release_year: 1990,\n        created_at: '2024-01-11T00:00:00Z',\n        updated_at: '2024-01-11T00:00:00Z',\n      },\n      {\n        id: '6',\n        title: '沉默的羔羊',\n        description: '一部心理惊悚片的经典之作，FBI学员克拉丽丝与食人魔汉尼拔博士的心理较量令人毛骨悚然。',\n        poster_url: '/posters/silence.jpg',\n        movie_link: 'https://example.com/silence',\n        category: 'thriller',\n        rating: 8.6,\n        release_year: 1991,\n        created_at: '2024-01-10T00:00:00Z',\n        updated_at: '2024-01-10T00:00:00Z',\n      },\n      {\n        id: '7',\n        title: '阿甘正传',\n        description: '一个智商不高但心地善良的男人的人生传奇。阿甘用他的纯真和坚持诠释了什么是真正的成功。',\n        poster_url: '/posters/forrest.jpg',\n        movie_link: 'https://example.com/forrest',\n        category: 'drama',\n        rating: 8.8,\n        release_year: 1994,\n        created_at: '2024-01-09T00:00:00Z',\n        updated_at: '2024-01-09T00:00:00Z',\n      },\n      {\n        id: '8',\n        title: '勇敢的心',\n        description: '苏格兰民族英雄威廉·华莱士为自由而战的史诗故事。梅尔·吉布森自导自演的历史巨作。',\n        poster_url: '/posters/braveheart.jpg',\n        movie_link: 'https://example.com/braveheart',\n        category: 'history',\n        rating: 8.4,\n        release_year: 1995,\n        created_at: '2024-01-08T00:00:00Z',\n        updated_at: '2024-01-08T00:00:00Z',\n      },\n      {\n        id: '9',\n        title: '终结者2：审判日',\n        description: '科幻动作片的里程碑之作，阿诺·施瓦辛格饰演的终结者成为了电影史上最经典的角色之一。',\n        poster_url: '/posters/terminator2.jpg',\n        movie_link: 'https://example.com/terminator2',\n        category: 'action',\n        rating: 8.5,\n        release_year: 1991,\n        created_at: '2024-01-07T00:00:00Z',\n        updated_at: '2024-01-07T00:00:00Z',\n      },\n      {\n        id: '10',\n        title: '罗马假日',\n        description: '奥黛丽·赫本和格利高里·派克主演的经典爱情喜剧，讲述了公主与记者在罗马的浪漫邂逅。',\n        poster_url: '/posters/roman.jpg',\n        movie_link: 'https://example.com/roman',\n        category: 'romance',\n        rating: 8.1,\n        release_year: 1953,\n        created_at: '2024-01-06T00:00:00Z',\n        updated_at: '2024-01-06T00:00:00Z',\n      },\n    ]\n    writeFileSync(MOVIES_FILE, JSON.stringify(initialMovies, null, 2))\n  }\n}\n\n// 音乐视频操作函数\nexport function getMusicVideos() {\n  try {\n    initializeData()\n    const data = readFileSync(MUSIC_FILE, 'utf8')\n    return JSON.parse(data)\n  } catch (error) {\n    console.error('Error reading music data:', error)\n    return []\n  }\n}\n\nexport function addMusicVideo(video: any) {\n  try {\n    const videos = getMusicVideos()\n    videos.push(video)\n    writeFileSync(MUSIC_FILE, JSON.stringify(videos, null, 2))\n    return true\n  } catch (error) {\n    console.error('Error adding music video:', error)\n    return false\n  }\n}\n\nexport function updateMusicVideo(id: string, updatedVideo: any) {\n  try {\n    const videos = getMusicVideos()\n    const index = videos.findIndex((v: any) => v.id === id)\n    if (index !== -1) {\n      videos[index] = updatedVideo\n      writeFileSync(MUSIC_FILE, JSON.stringify(videos, null, 2))\n      return true\n    }\n    return false\n  } catch (error) {\n    console.error('Error updating music video:', error)\n    return false\n  }\n}\n\nexport function deleteMusicVideo(id: string) {\n  try {\n    const videos = getMusicVideos()\n    const index = videos.findIndex((v: any) => v.id === id)\n    if (index !== -1) {\n      videos.splice(index, 1)\n      writeFileSync(MUSIC_FILE, JSON.stringify(videos, null, 2))\n      return true\n    }\n    return false\n  } catch (error) {\n    console.error('Error deleting music video:', error)\n    return false\n  }\n}\n\n// 电影操作函数\nexport function getMovies() {\n  try {\n    initializeData()\n    const data = readFileSync(MOVIES_FILE, 'utf8')\n    return JSON.parse(data)\n  } catch (error) {\n    console.error('Error reading movie data:', error)\n    return []\n  }\n}\n\nexport function addMovie(movie: any) {\n  try {\n    const movies = getMovies()\n    movies.push(movie)\n    writeFileSync(MOVIES_FILE, JSON.stringify(movies, null, 2))\n    return true\n  } catch (error) {\n    console.error('Error adding movie:', error)\n    return false\n  }\n}\n\nexport function updateMovie(id: string, updatedMovie: any) {\n  try {\n    const movies = getMovies()\n    const index = movies.findIndex((m: any) => m.id === id)\n    if (index !== -1) {\n      movies[index] = updatedMovie\n      writeFileSync(MOVIES_FILE, JSON.stringify(movies, null, 2))\n      return true\n    }\n    return false\n  } catch (error) {\n    console.error('Error updating movie:', error)\n    return false\n  }\n}\n\nexport function deleteMovie(id: string) {\n  try {\n    const movies = getMovies()\n    const index = movies.findIndex((m: any) => m.id === id)\n    if (index !== -1) {\n      movies.splice(index, 1)\n      writeFileSync(MOVIES_FILE, JSON.stringify(movies, null, 2))\n      return true\n    }\n    return false\n  } catch (error) {\n    console.error('Error deleting movie:', error)\n    return false\n  }\n}\n"], "names": [], "mappings": "AAAA,YAAY;;;;;;;;;;;AACZ;AACA;;;AAEA,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI;AACrC,MAAM,aAAa,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,UAAU;AAClC,MAAM,cAAc,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,UAAU;AAEnC,WAAW;AACX,SAAS;IACP,IAAI;QACF,MAAM;QACN,IAAI,CAAC,GAAG,UAAU,CAAC,WAAW;YAC5B,GAAG,SAAS,CAAC,UAAU;gBAAE,WAAW;YAAK;QAC3C;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;IAClD;AACF;AAEA,UAAU;AACV,SAAS;IACP;IAEA,eAAe;IACf,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,aAAa;QAC3B,CAAA,GAAA,6FAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,KAAK,SAAS,CAAC,EAAE,EAAE,MAAM;IACrD;IAEA,UAAU;IACV,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,cAAc;QAC5B,MAAM,gBAAgB;YACpB;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,QAAQ;gBACR,cAAc;gBACd,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,QAAQ;gBACR,cAAc;gBACd,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,QAAQ;gBACR,cAAc;gBACd,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,QAAQ;gBACR,cAAc;gBACd,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,QAAQ;gBACR,cAAc;gBACd,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,QAAQ;gBACR,cAAc;gBACd,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,QAAQ;gBACR,cAAc;gBACd,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,QAAQ;gBACR,cAAc;gBACd,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,QAAQ;gBACR,cAAc;gBACd,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,UAAU;gBACV,QAAQ;gBACR,cAAc;gBACd,YAAY;gBACZ,YAAY;YACd;SACD;QACD,CAAA,GAAA,6FAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,KAAK,SAAS,CAAC,eAAe,MAAM;IACjE;AACF;AAGO,SAAS;IACd,IAAI;QACF;QACA,MAAM,OAAO,CAAA,GAAA,6FAAA,CAAA,eAAY,AAAD,EAAE,YAAY;QACtC,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,EAAE;IACX;AACF;AAEO,SAAS,cAAc,KAAU;IACtC,IAAI;QACF,MAAM,SAAS;QACf,OAAO,IAAI,CAAC;QACZ,CAAA,GAAA,6FAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,KAAK,SAAS,CAAC,QAAQ,MAAM;QACvD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,SAAS,iBAAiB,EAAU,EAAE,YAAiB;IAC5D,IAAI;QACF,MAAM,SAAS;QACf,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;QACpD,IAAI,UAAU,CAAC,GAAG;YAChB,MAAM,CAAC,MAAM,GAAG;YAChB,CAAA,GAAA,6FAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,KAAK,SAAS,CAAC,QAAQ,MAAM;YACvD,OAAO;QACT;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAEO,SAAS,iBAAiB,EAAU;IACzC,IAAI;QACF,MAAM,SAAS;QACf,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;QACpD,IAAI,UAAU,CAAC,GAAG;YAChB,OAAO,MAAM,CAAC,OAAO;YACrB,CAAA,GAAA,6FAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,KAAK,SAAS,CAAC,QAAQ,MAAM;YACvD,OAAO;QACT;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF;AAGO,SAAS;IACd,IAAI;QACF;QACA,MAAM,OAAO,CAAA,GAAA,6FAAA,CAAA,eAAY,AAAD,EAAE,aAAa;QACvC,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,EAAE;IACX;AACF;AAEO,SAAS,SAAS,KAAU;IACjC,IAAI;QACF,MAAM,SAAS;QACf,OAAO,IAAI,CAAC;QACZ,CAAA,GAAA,6FAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,KAAK,SAAS,CAAC,QAAQ,MAAM;QACxD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;IACT;AACF;AAEO,SAAS,YAAY,EAAU,EAAE,YAAiB;IACvD,IAAI;QACF,MAAM,SAAS;QACf,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;QACpD,IAAI,UAAU,CAAC,GAAG;YAChB,MAAM,CAAC,MAAM,GAAG;YAChB,CAAA,GAAA,6FAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,KAAK,SAAS,CAAC,QAAQ,MAAM;YACxD,OAAO;QACT;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAEO,SAAS,YAAY,EAAU;IACpC,IAAI;QACF,MAAM,SAAS;QACf,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;QACpD,IAAI,UAAU,CAAC,GAAG;YAChB,OAAO,MAAM,CAAC,OAAO;YACrB,CAAA,GAAA,6FAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,KAAK,SAAS,CAAC,QAAQ,MAAM;YACxD,OAAO;QACT;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/%E7%94%9F%E6%B4%BB/%E9%A9%AC%E5%90%9B%E4%B8%AA%E4%BA%BA%E4%B8%BB%E9%A1%B5/music-portfolio/src/app/api/admin/movies/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { checkAdminAuth, requireAuth } from '@/lib/auth'\nimport {\n  getMovies,\n  addMovie,\n  updateMovie,\n  deleteMovie\n} from '@/lib/simple-store'\n\n// 获取所有电影\nexport async function GET(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n\n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  const movies = getMovies()\n  return NextResponse.json({\n    success: true,\n    data: movies\n  })\n}\n\n// 创建新电影\nexport async function POST(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  try {\n    const body = await request.json()\n    const { title, description, poster_url, movie_link, video_file_path, category, rating, release_year } = body\n\n    // 验证必填字段\n    if (!title || !description || !category) {\n      return NextResponse.json(\n        { error: 'Title, description, and category are required' },\n        { status: 400 }\n      )\n    }\n\n    // 验证分类\n    const validCategories = ['gangster', 'romance', 'history', 'thriller', 'drama', 'action']\n    if (!validCategories.includes(category)) {\n      return NextResponse.json(\n        { error: 'Invalid category' },\n        { status: 400 }\n      )\n    }\n\n    // 验证评分\n    if (rating && (rating < 0 || rating > 10)) {\n      return NextResponse.json(\n        { error: 'Rating must be between 0 and 10' },\n        { status: 400 }\n      )\n    }\n\n    // 验证年份\n    if (release_year && (release_year < 1900 || release_year > new Date().getFullYear())) {\n      return NextResponse.json(\n        { error: 'Invalid release year' },\n        { status: 400 }\n      )\n    }\n\n    const newMovie = {\n      id: Date.now().toString(),\n      title,\n      description,\n      poster_url: poster_url || null,\n      movie_link: movie_link || null,\n      video_file_path: video_file_path || null,\n      category,\n      rating: rating || null,\n      release_year: release_year || null,\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString(),\n    }\n\n    addMovie(newMovie)\n\n    return NextResponse.json({\n      success: true,\n      data: newMovie\n    })\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Invalid request body' },\n      { status: 400 }\n    )\n  }\n}\n\n// 更新电影\nexport async function PUT(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  try {\n    const body = await request.json()\n    const { id, title, description, poster_url, movie_link, video_file_path, category, rating, release_year } = body\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'Movie ID is required' },\n        { status: 400 }\n      )\n    }\n\n    const movies = getMovies()\n    const movie = movies.find(movie => movie.id === id)\n    if (!movie) {\n      return NextResponse.json(\n        { error: 'Movie not found' },\n        { status: 404 }\n      )\n    }\n\n    // 验证必填字段\n    if (!title || !description || !category) {\n      return NextResponse.json(\n        { error: 'Title, description, and category are required' },\n        { status: 400 }\n      )\n    }\n\n    // 验证分类\n    const validCategories = ['gangster', 'romance', 'history', 'thriller', 'drama', 'action']\n    if (!validCategories.includes(category)) {\n      return NextResponse.json(\n        { error: 'Invalid category' },\n        { status: 400 }\n      )\n    }\n\n    // 验证评分\n    if (rating && (rating < 0 || rating > 10)) {\n      return NextResponse.json(\n        { error: 'Rating must be between 0 and 10' },\n        { status: 400 }\n      )\n    }\n\n    // 验证年份\n    if (release_year && (release_year < 1900 || release_year > new Date().getFullYear())) {\n      return NextResponse.json(\n        { error: 'Invalid release year' },\n        { status: 400 }\n      )\n    }\n\n    const updatedMovie = {\n      ...movie,\n      title,\n      description,\n      poster_url: poster_url || null,\n      movie_link: movie_link || null,\n      video_file_path: video_file_path !== undefined ? video_file_path : movie.video_file_path,\n      category,\n      rating: rating || null,\n      release_year: release_year || null,\n      updated_at: new Date().toISOString(),\n    }\n\n    updateMovie(id, updatedMovie)\n\n    return NextResponse.json({\n      success: true,\n      data: updatedMovie\n    })\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Invalid request body' },\n      { status: 400 }\n    )\n  }\n}\n\n// 删除电影\nexport async function DELETE(request: NextRequest) {\n  const auth = checkAdminAuth(request)\n  \n  if (!auth.isAuthenticated) {\n    return requireAuth()\n  }\n\n  try {\n    const { searchParams } = new URL(request.url)\n    const id = searchParams.get('id')\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'Movie ID is required' },\n        { status: 400 }\n      )\n    }\n\n    const movies = getMovies()\n    const movie = movies.find(movie => movie.id === id)\n    if (!movie) {\n      return NextResponse.json(\n        { error: 'Movie not found' },\n        { status: 404 }\n      )\n    }\n\n    deleteMovie(id)\n\n    return NextResponse.json({\n      success: true,\n      data: movie\n    })\n  } catch (error) {\n    return NextResponse.json(\n      { error: 'Failed to delete movie' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAQO,eAAe,IAAI,OAAoB;IAC5C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD;IACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,MAAM;IACR;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;QAExG,SAAS;QACT,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgD,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,kBAAkB;YAAC;YAAY;YAAW;YAAW;YAAY;YAAS;SAAS;QACzF,IAAI,CAAC,gBAAgB,QAAQ,CAAC,WAAW;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,IAAI,UAAU,CAAC,SAAS,KAAK,SAAS,EAAE,GAAG;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,IAAI,gBAAgB,CAAC,eAAe,QAAQ,eAAe,IAAI,OAAO,WAAW,EAAE,GAAG;YACpF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW;YACf,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB;YACA;YACA,YAAY,cAAc;YAC1B,YAAY,cAAc;YAC1B,iBAAiB,mBAAmB;YACpC;YACA,QAAQ,UAAU;YAClB,cAAc,gBAAgB;YAC9B,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD,EAAE;QAET,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;QAE5G,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD;QACvB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAChD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAgD,GACzD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,MAAM,kBAAkB;YAAC;YAAY;YAAW;YAAW;YAAY;YAAS;SAAS;QACzF,IAAI,CAAC,gBAAgB,QAAQ,CAAC,WAAW;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,IAAI,UAAU,CAAC,SAAS,KAAK,SAAS,EAAE,GAAG;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO;QACP,IAAI,gBAAgB,CAAC,eAAe,QAAQ,eAAe,IAAI,OAAO,WAAW,EAAE,GAAG;YACpF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,eAAe;YACnB,GAAG,KAAK;YACR;YACA;YACA,YAAY,cAAc;YAC1B,YAAY,cAAc;YAC1B,iBAAiB,oBAAoB,YAAY,kBAAkB,MAAM,eAAe;YACxF;YACA,QAAQ,UAAU;YAClB,cAAc,gBAAgB;YAC9B,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD,EAAE,IAAI;QAEhB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,MAAM,OAAO,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;IAE5B,IAAI,CAAC,KAAK,eAAe,EAAE;QACzB,OAAO,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IACnB;IAEA,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;QAE5B,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD;QACvB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAChD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkB,GAC3B;gBAAE,QAAQ;YAAI;QAElB;QAEA,CAAA,GAAA,+HAAA,CAAA,cAAW,AAAD,EAAE;QAEZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}