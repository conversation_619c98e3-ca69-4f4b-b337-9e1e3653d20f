(()=>{var a={};a.id=698,a.ids=[698],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},1132:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/admin/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/admin/page.tsx","default")},2943:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]])},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24778:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>o});var d=c(60687),e=c(43210),f=c(62688);let g=(0,f.A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var h=c(85303),i=c(28440);let j=(0,f.A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),k=(0,f.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var l=c(2943);let m=(0,f.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),n=(0,f.A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]]);function o(){let[a,b]=(0,e.useState)("music"),[c,f]=(0,e.useState)(!1),[k,l]=(0,e.useState)(!0);if(k)return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-400",children:"加载中..."})]})});if(!c)return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsxs)("div",{className:"bg-gray-800 p-8 rounded-lg max-w-md w-full mx-4",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsx)(g,{className:"mx-auto text-blue-400 mb-4",size:48}),(0,d.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"后台管理系统"}),(0,d.jsx)("p",{className:"text-gray-400",children:"请登录以访问管理功能"})]}),(0,d.jsx)("button",{onClick:()=>{let a=prompt("请输入用户名:"),b=prompt("请输入密码:");if(a&&b){let c=btoa(`${a}:${b}`);fetch("/api/admin/auth",{method:"POST",headers:{Authorization:`Basic ${c}`}}).then(c=>{c.ok?(localStorage.setItem("admin_username",a),localStorage.setItem("admin_password",b),f(!0)):alert("认证失败，请检查用户名和密码")}).catch(a=>{console.error("Login error:",a),alert("登录失败，请重试")})}},className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-200",children:"登录"})]})});let m=[{id:"music",label:"音乐管理",icon:h.A},{id:"movie",label:"电影管理",icon:i.A},{id:"profile",label:"个人信息",icon:j}];return(0,d.jsx)("div",{className:"min-h-screen",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,d.jsxs)("div",{className:"mb-8 flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"后台管理系统"}),(0,d.jsx)("p",{className:"text-gray-400",children:"管理您的音乐和电影内容"})]}),(0,d.jsx)("button",{onClick:()=>{confirm("确定要退出登录吗？")&&(localStorage.removeItem("admin_username"),localStorage.removeItem("admin_password"),f(!1))},className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200",children:"退出登录"})]}),(0,d.jsx)("div",{className:"border-b border-gray-700 mb-8",children:(0,d.jsx)("nav",{className:"flex space-x-8",children:m.map(c=>{let e=c.icon;return(0,d.jsxs)("button",{onClick:()=>b(c.id),className:`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${a===c.id?"border-blue-500 text-blue-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}`,children:[(0,d.jsx)(e,{className:"mr-2",size:20}),c.label]},c.id)})})}),(0,d.jsxs)("div",{className:"bg-gray-800 rounded-lg p-6",children:["music"===a&&(0,d.jsx)(p,{}),"movie"===a&&(0,d.jsx)(q,{}),"profile"===a&&(0,d.jsx)(r,{})]})]})})}function p(){let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!0),[g,h]=(0,e.useState)(!1),[i,j]=(0,e.useState)(null),[o,p]=(0,e.useState)(!1),[q,r]=(0,e.useState)({title:"",artist:"",description:"",bilibili_url:"",thumbnail_url:""}),s=async()=>{try{let a=btoa(`${localStorage.getItem("admin_username")}:${localStorage.getItem("admin_password")}`),c=await fetch("/api/admin/music",{headers:{Authorization:`Basic ${a}`}});if(c.ok){let a=await c.json();b(a.data)}}catch(a){console.error("Failed to fetch music videos:",a)}finally{f(!1)}},t=async a=>{a.preventDefault();try{let a=btoa(`${localStorage.getItem("admin_username")}:${localStorage.getItem("admin_password")}`),b=i?"PUT":"POST",c=i?{...q,id:i.id}:q,d=await fetch("/api/admin/music",{method:b,headers:{"Content-Type":"application/json",Authorization:`Basic ${a}`},body:JSON.stringify(c)});if(d.ok)h(!1),j(null),r({title:"",artist:"",description:"",bilibili_url:"",thumbnail_url:""}),s();else{let a=await d.json();alert(a.error||"操作失败，请检查输入信息")}}catch(a){console.error("Failed to submit:",a),alert("操作失败")}},u=async a=>{if(confirm("确定要删除这个音乐视频吗？"))try{let b=btoa(`${localStorage.getItem("admin_username")}:${localStorage.getItem("admin_password")}`);(await fetch(`/api/admin/music?id=${a}`,{method:"DELETE",headers:{Authorization:`Basic ${b}`}})).ok?s():alert("删除失败")}catch(a){console.error("Failed to delete video:",a),alert("删除失败")}},v=async()=>{if(!q.bilibili_url)return void alert("请先输入B站视频链接");p(!0);try{let a=await fetch(`/api/bilibili?url=${encodeURIComponent(q.bilibili_url)}`),b=await a.json();b.success&&b.data.thumbnail?(r(a=>({...a,thumbnail_url:b.data.thumbnail,title:a.title||b.data.title||a.title})),alert("缩略图获取成功！")):alert("获取缩略图失败："+(b.error||"未知错误"))}catch(a){console.error("Error fetching thumbnail:",a),alert("获取缩略图失败，请检查网络连接")}finally{p(!1)}};return c?(0,d.jsx)("div",{className:"text-center py-8",children:"加载中..."}):(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold",children:"音乐视频管理"}),(0,d.jsxs)("button",{onClick:()=>h(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center",children:[(0,d.jsx)(k,{className:"mr-2",size:20}),"添加音乐视频"]})]}),g&&(0,d.jsxs)("div",{className:"bg-gray-700 p-6 rounded-lg mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:i?"编辑音乐视频":"添加新音乐视频"}),(0,d.jsxs)("form",{onSubmit:t,className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"视频标题 *"}),(0,d.jsx)("input",{type:"text",required:!0,value:q.title,onChange:a=>r({...q,title:a.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"演唱者 *"}),(0,d.jsx)("input",{type:"text",required:!0,value:q.artist,onChange:a=>r({...q,artist:a.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"B站视频链接 *"}),(0,d.jsx)("input",{type:"url",required:!0,placeholder:"https://www.bilibili.com/video/BV...",value:q.bilibili_url,onChange:a=>r({...q,bilibili_url:a.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"}),(0,d.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"支持格式：https://www.bilibili.com/video/BV... 或 https://b23.tv/..."})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"缩略图URL"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)("input",{type:"url",placeholder:"/thumbnails/video.jpg 或点击自动获取",value:q.thumbnail_url,onChange:a=>r({...q,thumbnail_url:a.target.value}),className:"flex-1 bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"}),(0,d.jsx)("button",{type:"button",onClick:v,disabled:o||!q.bilibili_url,className:"px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white rounded transition-colors duration-200 whitespace-nowrap",children:o?"获取中...":"自动获取"})]}),(0,d.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"留空将自动从B站获取缩略图，或手动输入图片URL"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"描述"}),(0,d.jsx)("textarea",{rows:3,value:q.description,onChange:a=>r({...q,description:a.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("button",{type:"submit",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded",children:i?"更新":"添加"}),(0,d.jsx)("button",{type:"button",onClick:()=>{h(!1),j(null),r({title:"",artist:"",description:"",bilibili_url:"",thumbnail_url:""})},className:"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded",children:"取消"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[a.map(a=>{var b;return(0,d.jsxs)("div",{className:"bg-gray-700 p-4 rounded-lg flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex items-start space-x-4 flex-1",children:[(0,d.jsxs)("div",{className:"flex-shrink-0",children:[a.thumbnail_url?(0,d.jsx)("img",{src:(b=a.thumbnail_url)?b.includes("bilibili.com")||b.includes("hdslb.com")?`/api/proxy-image?url=${encodeURIComponent(b)}`:b:"",alt:a.title,className:"w-20 h-12 object-cover rounded",onError:a=>{let b=a.target;b.style.display="none",b.nextElementSibling?.classList.remove("hidden")}}):null,(0,d.jsx)("div",{className:`w-20 h-12 bg-gray-600 rounded flex items-center justify-center ${a.thumbnail_url?"hidden":""}`,children:(0,d.jsx)(l.A,{className:"text-gray-400",size:16})})]}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-semibold",children:a.title}),(0,d.jsxs)("p",{className:"text-gray-400 text-sm",children:["演唱：",a.artist]}),a.description&&(0,d.jsx)("p",{className:"text-gray-500 text-sm mt-1 line-clamp-2",children:a.description}),(0,d.jsxs)("div",{className:"flex items-center mt-2 space-x-4",children:[(0,d.jsx)("a",{href:a.bilibili_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 text-sm",children:"在B站观看 →"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:new Date(a.created_at).toLocaleDateString("zh-CN")})]})]})]}),(0,d.jsxs)("div",{className:"flex space-x-2 ml-4",children:[(0,d.jsx)("button",{onClick:()=>{j(a),r({title:a.title,artist:a.artist,description:a.description||"",bilibili_url:a.bilibili_url,thumbnail_url:a.thumbnail_url||""}),h(!0)},className:"text-blue-400 hover:text-blue-300 p-2",title:"编辑",children:(0,d.jsx)(m,{size:20})}),(0,d.jsx)("button",{onClick:()=>u(a.id),className:"text-red-400 hover:text-red-300 p-2",title:"删除",children:(0,d.jsx)(n,{size:20})})]})]},a.id)}),0===a.length&&(0,d.jsx)("div",{className:"text-center py-8 text-gray-400",children:"暂无音乐视频"})]})]})}function q(){let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!0),[g,h]=(0,e.useState)(!1),[j,l]=(0,e.useState)(null),[o,p]=(0,e.useState)({title:"",description:"",poster_url:"",movie_link:"",video_file_path:"",category:"drama",rating:"",release_year:""}),[q,r]=(0,e.useState)(!1),[s,t]=(0,e.useState)(0),u=[{id:"gangster",label:"黑帮类"},{id:"romance",label:"情感类"},{id:"history",label:"历史类"},{id:"thriller",label:"惊悚类"},{id:"drama",label:"剧情类"},{id:"action",label:"动作类"}],v=async()=>{try{let a=btoa(`${localStorage.getItem("admin_username")}:${localStorage.getItem("admin_password")}`),c=await fetch("/api/admin/movies",{headers:{Authorization:`Basic ${a}`}});if(c.ok){let a=await c.json();b(a.data)}}catch(a){console.error("Failed to fetch movies:",a)}finally{f(!1)}},w=async a=>{a.preventDefault();try{let a=btoa(`${localStorage.getItem("admin_username")}:${localStorage.getItem("admin_password")}`),b=j?"PUT":"POST",c={...o,rating:o.rating?parseFloat(o.rating):null,release_year:o.release_year?parseInt(o.release_year):null,...j&&{id:j.id}},d=await fetch("/api/admin/movies",{method:b,headers:{"Content-Type":"application/json",Authorization:`Basic ${a}`},body:JSON.stringify(c)});if(d.ok)h(!1),l(null),p({title:"",description:"",poster_url:"",movie_link:"",category:"drama",rating:"",release_year:""}),v();else{let a=await d.json();alert(a.error||"操作失败，请检查输入信息")}}catch(a){console.error("Failed to submit:",a),alert("操作失败")}},x=async a=>{if(confirm("确定要删除这部电影吗？"))try{let b=btoa(`${localStorage.getItem("admin_username")}:${localStorage.getItem("admin_password")}`);(await fetch(`/api/admin/movies?id=${a}`,{method:"DELETE",headers:{Authorization:`Basic ${b}`}})).ok?v():alert("删除失败")}catch(a){console.error("Failed to delete movie:",a),alert("删除失败")}},y=async a=>{let b=a.target.files?.[0];if(b){r(!0),t(0);try{let a=new FormData;a.append("video",b);let c=btoa(`${localStorage.getItem("admin_username")}:${localStorage.getItem("admin_password")}`),d=setInterval(()=>{t(a=>Math.min(a+10,90))},200),e=await fetch("/api/upload/video",{method:"POST",headers:{Authorization:`Basic ${c}`},body:a});if(clearInterval(d),t(100),e.ok){let a=await e.json();p(b=>({...b,video_file_path:a.data.filePath})),alert("视频上传成功！")}else{let a=await e.json();alert("上传失败："+(a.error||"未知错误"))}}catch(a){console.error("Upload error:",a),alert("上传失败，请检查网络连接")}finally{r(!1),t(0)}}};return c?(0,d.jsx)("div",{className:"text-center py-8",children:"加载中..."}):(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold",children:"电影管理"}),(0,d.jsxs)("button",{onClick:()=>h(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center",children:[(0,d.jsx)(k,{className:"mr-2",size:20}),"添加电影"]})]}),g&&(0,d.jsxs)("div",{className:"bg-gray-700 p-6 rounded-lg mb-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:j?"编辑电影":"添加新电影"}),(0,d.jsxs)("form",{onSubmit:w,className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"电影名称 *"}),(0,d.jsx)("input",{type:"text",required:!0,value:o.title,onChange:a=>p({...o,title:a.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"分类 *"}),(0,d.jsx)("select",{required:!0,value:o.category,onChange:a=>p({...o,category:a.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500",children:u.map(a=>(0,d.jsx)("option",{value:a.id,children:a.label},a.id))})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"电影描述 *"}),(0,d.jsx)("textarea",{rows:4,required:!0,value:o.description,onChange:a=>p({...o,description:a.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"海报图片URL"}),(0,d.jsx)("input",{type:"url",placeholder:"/posters/movie.jpg",value:o.poster_url,onChange:a=>p({...o,poster_url:a.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"观看链接"}),(0,d.jsx)("input",{type:"url",placeholder:"https://example.com/movie",value:o.movie_link,onChange:a=>p({...o,movie_link:a.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"视频文件上传"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("input",{type:"file",accept:"video/mp4,video/quicktime,video/x-msvideo,video/webm,video/x-ms-wmv,video/x-flv",onChange:y,disabled:q,className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-600 file:text-white hover:file:bg-blue-700 disabled:opacity-50"}),q&&(0,d.jsx)("div",{className:"w-full bg-gray-600 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${s}%`}})}),o.video_file_path&&(0,d.jsxs)("div",{className:"text-sm text-green-400",children:["✓ 已上传视频文件: ",o.video_file_path.split("/").pop()]}),(0,d.jsx)("p",{className:"text-xs text-gray-400",children:"支持格式：MP4, MOV, AVI, WebM, WMV, FLV（最大500MB）"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"评分 (0-10)"}),(0,d.jsx)("input",{type:"number",min:"0",max:"10",step:"0.1",placeholder:"8.5",value:o.rating,onChange:a=>p({...o,rating:a.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"上映年份"}),(0,d.jsx)("input",{type:"number",min:"1900",max:new Date().getFullYear(),placeholder:"2023",value:o.release_year,onChange:a=>p({...o,release_year:a.target.value}),className:"w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-blue-500"})]})]}),(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsx)("button",{type:"submit",className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded",children:j?"更新":"添加"}),(0,d.jsx)("button",{type:"button",onClick:()=>{h(!1),l(null),p({title:"",description:"",poster_url:"",movie_link:"",video_file_path:"",category:"drama",rating:"",release_year:""})},className:"bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded",children:"取消"})]})]})]}),(0,d.jsxs)("div",{className:"grid gap-4",children:[a.map(a=>(0,d.jsxs)("div",{className:"bg-gray-700 p-4 rounded-lg flex items-start justify-between",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:a.poster_url?(0,d.jsx)("img",{src:a.poster_url,alt:a.title,className:"w-16 h-24 object-cover rounded"}):(0,d.jsx)("div",{className:"w-16 h-24 bg-gray-600 rounded flex items-center justify-center",children:(0,d.jsx)(i.A,{className:"text-gray-400",size:24})})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h3",{className:"font-semibold text-lg",children:a.title}),(0,d.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-400 mb-2",children:[(0,d.jsx)("span",{className:"px-2 py-1 bg-gray-600 rounded text-xs",children:u.find(b=>b.id===a.category)?.label}),a.rating&&(0,d.jsxs)("span",{className:"flex items-center",children:["⭐ ",a.rating]}),a.release_year&&(0,d.jsx)("span",{children:a.release_year})]}),(0,d.jsx)("p",{className:"text-gray-300 text-sm line-clamp-3 mb-2",children:a.description}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[a.movie_link&&(0,d.jsx)("a",{href:a.movie_link,target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 text-sm",children:"观看电影 →"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:new Date(a.created_at).toLocaleDateString("zh-CN")})]})]})]})}),(0,d.jsxs)("div",{className:"flex space-x-2 ml-4",children:[(0,d.jsx)("button",{onClick:()=>{l(a),p({title:a.title,description:a.description,poster_url:a.poster_url||"",movie_link:a.movie_link||"",video_file_path:a.video_file_path||"",category:a.category,rating:a.rating?a.rating.toString():"",release_year:a.release_year?a.release_year.toString():""}),h(!0)},className:"text-blue-400 hover:text-blue-300 p-2",title:"编辑",children:(0,d.jsx)(m,{size:20})}),(0,d.jsx)("button",{onClick:()=>x(a.id),className:"text-red-400 hover:text-red-300 p-2",title:"删除",children:(0,d.jsx)(n,{size:20})})]})]},a.id)),0===a.length&&(0,d.jsx)("div",{className:"text-center py-8 text-gray-400",children:"暂无电影信息"})]})]})}function r(){return(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"个人信息管理"}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"姓名"}),(0,d.jsx)("input",{type:"text",defaultValue:"马君",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"个人简介"}),(0,d.jsx)("textarea",{rows:4,defaultValue:"音乐创作者 \xb7 视频制作人",className:"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 focus:outline-none focus:border-blue-500"})]}),(0,d.jsx)("button",{className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg",children:"保存更改"})]})]})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},28440:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("film",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M7 3v18",key:"bbkbws"}],["path",{d:"M3 7.5h4",key:"zfgn84"}],["path",{d:"M3 12h18",key:"1i2n21"}],["path",{d:"M3 16.5h4",key:"1230mu"}],["path",{d:"M17 3v18",key:"in4fa5"}],["path",{d:"M17 7.5h4",key:"myr1c1"}],["path",{d:"M17 16.5h4",key:"go4c1d"}]])},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},45721:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},52068:(a,b,c)=>{Promise.resolve().then(c.bind(c,1132))},52535:(a,b,c)=>{Promise.resolve().then(c.bind(c,88928))},58745:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},61135:()=>{},62198:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1132)),"/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/admin/page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,98042)),"/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/admin/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/admin/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},80559:(a,b,c)=>{Promise.resolve().then(c.bind(c,86246))},81924:(a,b,c)=>{Promise.resolve().then(c.bind(c,24778))},85303:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]])},86246:(a,b,c)=>{"use strict";c.d(b,{default:()=>k});var d=c(60687),e=c(85814),f=c.n(e),g=c(16189),h=c(43210),i=c(11860),j=c(12941);let k=()=>{let a=(0,g.usePathname)(),[b,c]=(0,h.useState)(!1),e=[{href:"/",label:"HOME",key:"home"},{href:"/music",label:"MUSIC",key:"music"},{href:"/movie",label:"MOVIES",key:"movie"}],k=b=>"/"===b?"/"===a:a.startsWith(b);return(0,d.jsx)("nav",{className:"bg-black fixed w-full z-50",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,d.jsx)(f(),{href:"/",className:"text-white text-2xl font-bold tracking-wider",children:(0,d.jsx)("span",{className:"font-serif italic",children:"KIMAHALA"})}),(0,d.jsxs)("div",{className:"hidden md:flex items-center space-x-8",children:[e.map(a=>(0,d.jsx)(f(),{href:a.href,className:`px-3 py-2 text-sm font-semibold tracking-wider transition-colors duration-200 ${k(a.href)?"text-red-400":"text-gray-300 hover:text-red-400"}`,children:a.label},a.key)),(0,d.jsx)("div",{className:"flex items-center space-x-3 ml-6",children:(0,d.jsx)("a",{href:"https://space.bilibili.com/27744192?spm_id_from=333.1007.0.0",target:"_blank",rel:"noopener noreferrer",className:"text-red-400 hover:text-white transition-colors duration-200",title:"B站主页",children:(0,d.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,d.jsx)("path",{d:"M17.813 4.653h.854c1.51.054 2.769.578 3.773 1.574 1.004.995 1.524 2.249 1.56 3.76v7.36c-.036 1.51-.556 2.769-1.56 3.773s-2.262 1.524-3.773 1.56H5.333c-1.51-.036-2.769-.556-3.773-1.56S.036 18.858 0 17.347v-7.36c.036-1.511.556-2.765 1.56-3.76 1.004-.996 2.262-1.52 3.773-1.574h.774l-1.174-1.12a1.234 1.234 0 0 1-.373-.906c0-.356.124-.658.373-.907l.027-.027c.267-.249.573-.373.92-.373.347 0 .653.124.92.373L9.653 4.44c.071.071.134.142.187.213h4.267a.836.836 0 0 1 .16-.213l2.853-2.747c.267-.249.573-.373.92-.373.347 0 .662.151.929.4.267.249.391.551.391.907 0 .356-.124.657-.373.906l-1.174 1.12zM5.333 7.24c-.746.018-1.373.276-1.88.773-.506.498-.769 1.13-.789 1.894v7.52c.02.764.283 1.396.789 1.894.507.498 1.134.756 1.88.773h13.334c.746-.017 1.373-.275 1.88-.773.506-.498.769-1.13.789-1.894v-7.52c-.02-.765-.283-1.396-.789-1.894-.507-.497-1.134-.755-1.88-.773H5.333zM8 11.107c.373 0 .684.124.933.373.25.249.383.569.4.96v1.173c-.017.391-.15.711-.4.96-.249.249-.56.373-.933.373s-.684-.124-.933-.373c-.25-.249-.383-.569-.4-.96V12.44c.017-.391.15-.711.4-.96.249-.249.56-.373.933-.373zm8 0c.373 0 .684.124.933.373.25.249.383.569.4.96v1.173c-.017.391-.15.711-.4.96-.249.249-.56.373-.933.373s-.684-.124-.933-.373c-.25-.249-.383-.569-.4-.96V12.44c.017-.391.15-.711.4-.96.249-.249.56-.373.933-.373z"})})})})]}),(0,d.jsxs)("div",{className:"md:hidden flex items-center space-x-3",children:[(0,d.jsx)("a",{href:"https://space.bilibili.com/27744192?spm_id_from=333.1007.0.0",target:"_blank",rel:"noopener noreferrer",className:"text-red-400 hover:text-white transition-colors duration-200",title:"B站主页",children:(0,d.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,d.jsx)("path",{d:"M17.813 4.653h.854c1.51.054 2.769.578 3.773 1.574 1.004.995 1.524 2.249 1.56 3.76v7.36c-.036 1.51-.556 2.769-1.56 3.773s-2.262 1.524-3.773 1.56H5.333c-1.51-.036-2.769-.556-3.773-1.56S.036 18.858 0 17.347v-7.36c.036-1.511.556-2.765 1.56-3.76 1.004-.996 2.262-1.52 3.773-1.574h.774l-1.174-1.12a1.234 1.234 0 0 1-.373-.906c0-.356.124-.658.373-.907l.027-.027c.267-.249.573-.373.92-.373.347 0 .653.124.92.373L9.653 4.44c.071.071.134.142.187.213h4.267a.836.836 0 0 1 .16-.213l2.853-2.747c.267-.249.573-.373.92-.373.347 0 .662.151.929.4.267.249.391.551.391.907 0 .356-.124.657-.373.906l-1.174 1.12zM5.333 7.24c-.746.018-1.373.276-1.88.773-.506.498-.769 1.13-.789 1.894v7.52c.02.764.283 1.396.789 1.894.507.498 1.134.756 1.88.773h13.334c.746-.017 1.373-.275 1.88-.773.506-.498.769-1.13.789-1.894v-7.52c-.02-.765-.283-1.396-.789-1.894-.507-.497-1.134-.755-1.88-.773H5.333zM8 11.107c.373 0 .684.124.933.373.25.249.383.569.4.96v1.173c-.017.391-.15.711-.4.96-.249.249-.56.373-.933.373s-.684-.124-.933-.373c-.25-.249-.383-.569-.4-.96V12.44c.017-.391.15-.711.4-.96.249-.249.56-.373.933-.373zm8 0c.373 0 .684.124.933.373.25.249.383.569.4.96v1.173c-.017.391-.15.711-.4.96-.249.249-.56.373-.933.373s-.684-.124-.933-.373c-.25-.249-.383-.569-.4-.96V12.44c.017-.391.15-.711.4-.96.249-.249.56-.373.933-.373z"})})}),(0,d.jsx)("button",{onClick:()=>c(!b),className:"text-gray-300 hover:text-white p-2",children:b?(0,d.jsx)(i.A,{size:24}):(0,d.jsx)(j.A,{size:24})})]})]}),b&&(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 bg-black",children:e.map(a=>(0,d.jsx)(f(),{href:a.href,className:`block px-3 py-2 text-base font-semibold tracking-wider transition-colors duration-200 ${k(a.href)?"text-red-400 bg-gray-800":"text-gray-300 hover:text-red-400 hover:bg-gray-700"}`,onClick:()=>c(!1),children:a.label},a.key))})})]})})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88928:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/components/Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/components/Navigation.tsx","default")},98042:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h,metadata:()=>g});var d=c(37413);c(61135);var e=c(88928);let f=()=>(0,d.jsx)("footer",{className:"bg-black text-white py-8 mt-16",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"text-gray-400 text-sm tracking-wide",children:"\xa9 2024 KIMAHALA. All rights reserved."}),(0,d.jsxs)("div",{className:"mt-4 flex justify-center space-x-6",children:[(0,d.jsx)("a",{href:"https://space.bilibili.com/27744192?spm_id_from=333.1007.0.0",target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-red-400 transition-colors duration-200 tracking-wide",children:"BILIBILI"}),(0,d.jsx)("a",{href:"#",className:"text-gray-400 hover:text-red-400 transition-colors duration-200 tracking-wide",children:"WEIBO"}),(0,d.jsx)("a",{href:"#",className:"text-gray-400 hover:text-red-400 transition-colors duration-200 tracking-wide",children:"EMAIL"})]})]})})}),g={title:"KIMAHALA - Musician & Content Creator",description:"KIMAHALA's official website featuring original music and creative video content"};function h({children:a}){return(0,d.jsx)("html",{lang:"zh-CN",children:(0,d.jsxs)("body",{className:"antialiased bg-gray-900 text-white min-h-screen font-sans",children:[(0,d.jsx)(e.default,{}),(0,d.jsx)("main",{className:"pt-16",children:a}),(0,d.jsx)(f,{})]})})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,862],()=>b(b.s=62198));module.exports=c})();