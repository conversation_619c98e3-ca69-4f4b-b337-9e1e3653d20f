(()=>{var a={};a.id=625,a.ids=[625],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56605:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>C,patchFetch:()=>B,routeModule:()=>x,serverHooks:()=>A,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>z});var d={};c.r(d),c.d(d,{GET:()=>w});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(82461);async function w(){try{let a=(0,v.EK)();return u.NextResponse.json({success:!0,data:a})}catch(a){return console.error("Error fetching movies:",a),u.NextResponse.json({success:!1,data:[]})}}let x=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/movies/route",pathname:"/api/movies",filename:"route",bundlePath:"app/api/movies/route"},distDir:".next",projectDir:"",resolvedPagePath:"/Users/<USER>/Desktop/生活/马君个人主页/music-portfolio/src/app/api/movies/route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:y,workUnitAsyncStorage:z,serverHooks:A}=x;function B(){return(0,g.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:z})}async function C(a,b,c){var d;let e="/api/movies/route";"/index"===e&&(e="/");let g=await x.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:y,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!y){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||x.isDev||y||(G="/index"===(G=D)?"/":G);let H=!0===x.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>x.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>x.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await x.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await x.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),y&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await x.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},82461:(a,b,c)=>{"use strict";c.d(b,{Cb:()=>j,EK:()=>n,Em:()=>k,GW:()=>p,Kt:()=>l,kb:()=>o,mN:()=>q,oF:()=>m});var d=c(29021),e=c(33873);let f=(0,e.join)(process.cwd(),"data"),g=(0,e.join)(f,"music.json"),h=(0,e.join)(f,"movies.json");function i(){try{let a=c(29021);a.existsSync(f)||a.mkdirSync(f,{recursive:!0})}catch(a){console.error("Error creating data directory:",a)}(0,d.existsSync)(g)||(0,d.writeFileSync)(g,JSON.stringify([],null,2)),(0,d.existsSync)(h)||(0,d.writeFileSync)(h,JSON.stringify([{id:"1",title:"教父",description:"一个黑手党家族的传奇故事，展现了权力、家族和忠诚的复杂关系。弗朗西斯\xb7福特\xb7科波拉执导的经典之作，被誉为电影史上最伟大的作品之一。",poster_url:"/posters/godfather.jpg",movie_link:"https://example.com/godfather",category:"gangster",rating:9.3,release_year:1972,created_at:"2024-01-15T00:00:00Z",updated_at:"2024-01-15T00:00:00Z"},{id:"2",title:"肖申克的救赎",description:"一个关于希望、友谊和救赎的感人故事。安迪在监狱中的经历告诉我们，即使在最黑暗的地方，希望之光也永远不会熄灭。",poster_url:"/posters/shawshank.jpg",movie_link:"https://example.com/shawshank",category:"drama",rating:9.7,release_year:1994,created_at:"2024-01-14T00:00:00Z",updated_at:"2024-01-14T00:00:00Z"},{id:"3",title:"泰坦尼克号",description:"一个跨越阶级的爱情故事，在历史上最著名的海难背景下展开。杰克和露丝的爱情故事感动了全世界观众。",poster_url:"/posters/titanic.jpg",movie_link:"https://example.com/titanic",category:"romance",rating:7.9,release_year:1997,created_at:"2024-01-13T00:00:00Z",updated_at:"2024-01-13T00:00:00Z"},{id:"4",title:"辛德勒的名单",description:"二战期间，德国商人奥斯卡\xb7辛德勒拯救犹太人的真实故事。这部黑白电影以其深刻的人道主义精神震撼人心。",poster_url:"/posters/schindler.jpg",movie_link:"https://example.com/schindler",category:"history",rating:9,release_year:1993,created_at:"2024-01-12T00:00:00Z",updated_at:"2024-01-12T00:00:00Z"},{id:"5",title:"好家伙",description:"基于真实事件改编的黑帮电影，讲述了亨利\xb7希尔在黑手党中的生活经历。马丁\xb7斯科塞斯的又一经典之作。",poster_url:"/posters/goodfellas.jpg",movie_link:"https://example.com/goodfellas",category:"gangster",rating:8.7,release_year:1990,created_at:"2024-01-11T00:00:00Z",updated_at:"2024-01-11T00:00:00Z"},{id:"6",title:"沉默的羔羊",description:"一部心理惊悚片的经典之作，FBI学员克拉丽丝与食人魔汉尼拔博士的心理较量令人毛骨悚然。",poster_url:"/posters/silence.jpg",movie_link:"https://example.com/silence",category:"thriller",rating:8.6,release_year:1991,created_at:"2024-01-10T00:00:00Z",updated_at:"2024-01-10T00:00:00Z"},{id:"7",title:"阿甘正传",description:"一个智商不高但心地善良的男人的人生传奇。阿甘用他的纯真和坚持诠释了什么是真正的成功。",poster_url:"/posters/forrest.jpg",movie_link:"https://example.com/forrest",category:"drama",rating:8.8,release_year:1994,created_at:"2024-01-09T00:00:00Z",updated_at:"2024-01-09T00:00:00Z"},{id:"8",title:"勇敢的心",description:"苏格兰民族英雄威廉\xb7华莱士为自由而战的史诗故事。梅尔\xb7吉布森自导自演的历史巨作。",poster_url:"/posters/braveheart.jpg",movie_link:"https://example.com/braveheart",category:"history",rating:8.4,release_year:1995,created_at:"2024-01-08T00:00:00Z",updated_at:"2024-01-08T00:00:00Z"},{id:"9",title:"终结者2：审判日",description:"科幻动作片的里程碑之作，阿诺\xb7施瓦辛格饰演的终结者成为了电影史上最经典的角色之一。",poster_url:"/posters/terminator2.jpg",movie_link:"https://example.com/terminator2",category:"action",rating:8.5,release_year:1991,created_at:"2024-01-07T00:00:00Z",updated_at:"2024-01-07T00:00:00Z"},{id:"10",title:"罗马假日",description:"奥黛丽\xb7赫本和格利高里\xb7派克主演的经典爱情喜剧，讲述了公主与记者在罗马的浪漫邂逅。",poster_url:"/posters/roman.jpg",movie_link:"https://example.com/roman",category:"romance",rating:8.1,release_year:1953,created_at:"2024-01-06T00:00:00Z",updated_at:"2024-01-06T00:00:00Z"}],null,2))}function j(){try{i();let a=(0,d.readFileSync)(g,"utf8");return JSON.parse(a)}catch(a){return console.error("Error reading music data:",a),[]}}function k(a){try{let b=j();return b.push(a),(0,d.writeFileSync)(g,JSON.stringify(b,null,2)),!0}catch(a){return console.error("Error adding music video:",a),!1}}function l(a,b){try{let c=j(),e=c.findIndex(b=>b.id===a);if(-1!==e)return c[e]=b,(0,d.writeFileSync)(g,JSON.stringify(c,null,2)),!0;return!1}catch(a){return console.error("Error updating music video:",a),!1}}function m(a){try{let b=j(),c=b.findIndex(b=>b.id===a);if(-1!==c)return b.splice(c,1),(0,d.writeFileSync)(g,JSON.stringify(b,null,2)),!0;return!1}catch(a){return console.error("Error deleting music video:",a),!1}}function n(){try{i();let a=(0,d.readFileSync)(h,"utf8");return JSON.parse(a)}catch(a){return console.error("Error reading movie data:",a),[]}}function o(a){try{let b=n();return b.push(a),(0,d.writeFileSync)(h,JSON.stringify(b,null,2)),!0}catch(a){return console.error("Error adding movie:",a),!1}}function p(a,b){try{let c=n(),e=c.findIndex(b=>b.id===a);if(-1!==e)return c[e]=b,(0,d.writeFileSync)(h,JSON.stringify(c,null,2)),!0;return!1}catch(a){return console.error("Error updating movie:",a),!1}}function q(a){try{let b=n(),c=b.findIndex(b=>b.id===a);if(-1!==c)return b.splice(c,1),(0,d.writeFileSync)(h,JSON.stringify(b,null,2)),!0;return!1}catch(a){return console.error("Error deleting movie:",a),!1}}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,55],()=>b(b.s=56605));module.exports=c})();