'use client'

import { useState, useEffect } from 'react'
import { Film, Star, ExternalLink, Calendar } from 'lucide-react'
import Image from 'next/image'

interface Movie {
  id: string
  title: string
  description: string
  poster_url?: string
  movie_link?: string
  category: string
  rating?: number
  release_year?: number
  created_at: string
}

export default function MoviePage() {
  const [movies, setMovies] = useState<Movie[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [isLoading, setIsLoading] = useState(true)

  const categories = [
    { id: 'all', label: '全部' },
    { id: 'gangster', label: '黑帮类' },
    { id: 'romance', label: '情感类' },
    { id: 'history', label: '历史类' },
    { id: 'thriller', label: '惊悚类' },
    { id: 'drama', label: '剧情类' },
    { id: 'action', label: '动作类' },
  ]

  useEffect(() => {
    fetchMovies()
  }, [])

  const fetchMovies = async () => {
    try {
      const response = await fetch('/api/movies')
      if (response.ok) {
        const data = await response.json()
        setMovies(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch movies:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredMovies = selectedCategory === 'all' 
    ? movies 
    : movies.filter(movie => movie.category === selectedCategory)

  if (isLoading) {
    return (
      <div className="min-h-screen py-12 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading movies...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <Film className="text-red-400" size={48} />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-wider">MOVIES</h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Discover the films that inspire me - from classic gangster movies to emotional dramas and historical epics
          </p>
        </div>

        {/* Category Filter */}
        <div className="mb-12">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-full font-semibold transition-all duration-200 ${
                  selectedCategory === category.id
                    ? 'bg-red-600 text-white shadow-lg'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>

        {/* Movies Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {filteredMovies.map((movie) => (
            <div
              key={movie.id}
              className="bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-all duration-300 hover:scale-105 hover:shadow-xl"
            >
              {/* Movie Poster */}
              <div className="relative aspect-[2/3] bg-gray-600">
                {movie.poster_url ? (
                  <Image
                    src={movie.poster_url}
                    alt={movie.title}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <Film className="text-gray-400" size={64} />
                  </div>
                )}
                
                {/* Rating Badge */}
                {movie.rating && (
                  <div className="absolute top-4 right-4 bg-black/70 backdrop-blur-sm rounded-full px-3 py-1 flex items-center">
                    <Star className="text-yellow-400 mr-1" size={16} fill="currentColor" />
                    <span className="text-white text-sm font-semibold">{movie.rating}</span>
                  </div>
                )}

                {/* External Link Button */}
                {movie.movie_link && (
                  <div className="absolute bottom-4 right-4">
                    <a
                      href={movie.movie_link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center transition-colors duration-200"
                      title="观看电影"
                    >
                      <ExternalLink className="text-white" size={20} />
                    </a>
                  </div>
                )}
              </div>

              {/* Movie Info */}
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2 line-clamp-2">{movie.title}</h3>
                
                {/* Category and Year */}
                <div className="flex items-center justify-between mb-3">
                  <span className="px-3 py-1 bg-gray-700 text-gray-300 text-sm rounded-full">
                    {categories.find(cat => cat.id === movie.category)?.label || movie.category}
                  </span>
                  {movie.release_year && (
                    <div className="flex items-center text-gray-400 text-sm">
                      <Calendar className="mr-1" size={14} />
                      <span>{movie.release_year}</span>
                    </div>
                  )}
                </div>

                {/* Description */}
                <p className="text-gray-400 text-sm line-clamp-3 mb-4">
                  {movie.description}
                </p>

                {/* Action Button */}
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">
                    添加于 {new Date(movie.created_at).toLocaleDateString('zh-CN')}
                  </span>
                  {movie.movie_link && (
                    <a
                      href={movie.movie_link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-red-400 hover:text-red-300 text-sm font-semibold transition-colors duration-200"
                    >
                      观看 →
                    </a>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredMovies.length === 0 && (
          <div className="text-center py-20">
            <Film className="mx-auto text-gray-600 mb-4" size={64} />
            <h3 className="text-2xl font-semibold text-gray-400 mb-2">
              {selectedCategory === 'all' ? 'No Movies Available' : 'No Movies in This Category'}
            </h3>
            <p className="text-gray-500">
              {selectedCategory === 'all' 
                ? 'Stay tuned for movie recommendations' 
                : 'Try selecting a different category'}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
