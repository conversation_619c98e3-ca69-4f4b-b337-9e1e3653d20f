import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import { checkAdminAuth, requireAuth } from '@/lib/auth'

// 支持的视频格式
const ALLOWED_VIDEO_TYPES = [
  'video/mp4',
  'video/quicktime', // .mov
  'video/x-msvideo', // .avi
  'video/webm',
  'video/x-ms-wmv', // .wmv
  'video/x-flv', // .flv
]

// 文件大小限制 (500MB)
const MAX_FILE_SIZE = 5000 * 1024 * 1024

export async function POST(request: NextRequest) {
  try {
    // 检查管理员权限
    const auth = checkAdminAuth(request)
    if (!auth.isAuthenticated) {
      return requireAuth()
    }

    const formData = await request.formData()
    const file = formData.get('video') as File

    if (!file) {
      return NextResponse.json(
        { error: 'No video file provided' },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!ALLOWED_VIDEO_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: `Unsupported file type. Allowed types: ${ALLOWED_VIDEO_TYPES.join(', ')}` },
        { status: 400 }
      )
    }

    // 验证文件大小
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: `File too large. Maximum size: ${MAX_FILE_SIZE / (1024 * 1024)}MB` },
        { status: 400 }
      )
    }

    // 创建上传目录
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'movies')
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // 生成唯一文件名
    const timestamp = Date.now()
    const fileExtension = path.extname(file.name)
    const fileName = `movie_${timestamp}${fileExtension}`
    const filePath = path.join(uploadDir, fileName)

    // 保存文件
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // 返回相对路径（用于数据库存储）
    const relativePath = `/uploads/movies/${fileName}`

    return NextResponse.json({
      success: true,
      data: {
        fileName,
        filePath: relativePath,
        fileSize: file.size,
        fileType: file.type
      }
    })

  } catch (error) {
    console.error('Error uploading video:', error)
    return NextResponse.json(
      { error: 'Failed to upload video file' },
      { status: 500 }
    )
  }
}

// 获取上传进度的辅助端点
export async function GET(request: NextRequest) {
  const auth = checkAdminAuth(request)
  if (!auth.isAuthenticated) {
    return requireAuth()
  }

  return NextResponse.json({
    success: true,
    maxFileSize: MAX_FILE_SIZE,
    allowedTypes: ALLOWED_VIDEO_TYPES
  })
}
