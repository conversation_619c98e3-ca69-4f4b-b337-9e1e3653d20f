'use client'

import { useState, useEffect } from 'react'
import { Play, Music, Video, ExternalLink } from 'lucide-react'

// 处理图片URL的辅助函数
function getProxiedImageUrl(originalUrl: string): string {
  if (!originalUrl) return ''

  // 如果是B站的图片URL，使用我们的代理
  if (originalUrl.includes('bilibili.com') || originalUrl.includes('hdslb.com')) {
    return `/api/proxy-image?url=${encodeURIComponent(originalUrl)}`
  }

  // 其他图片直接返回原URL
  return originalUrl
}

interface MusicVideo {
  id: string
  title: string
  artist: string
  description?: string
  bilibili_url: string
  bilibili_embed_id: string
  thumbnail_url?: string
  created_at: string
}

export default function MusicPage() {
  const [musicVideos, setMusicVideos] = useState<MusicVideo[]>([])
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchMusicVideos()
  }, [])

  const fetchMusicVideos = async () => {
    try {
      // 使用公开的API接口获取音乐视频数据
      const response = await fetch('/api/music')
      if (response.ok) {
        const data = await response.json()
        setMusicVideos(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch music videos:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getBilibiliEmbedUrl = (embedId: string) => {
    return `//player.bilibili.com/player.html?bvid=${embedId}&page=1&high_quality=1&danmaku=0`
  }

  const handleVideoSelect = (videoId: string) => {
    setSelectedVideo(videoId)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen py-12 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-400 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading music...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <Music className="text-red-400" size={48} />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-wider">MUSIC</h1>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Watch my music videos on Bilibili, including original compositions, covers, and music creation process
          </p>
        </div>

        {/* Video Player Section */}
        {selectedVideo && (
          <div className="mb-12">
            <div className="bg-gray-800 rounded-lg p-6">
              <div className="video-container">
                <iframe
                  src={getBilibiliEmbedUrl(selectedVideo)}
                  scrolling="no"
                  border="0"
                  frameBorder="no"
                  allowFullScreen
                  title="Bilibili Video Player"
                  className="w-full aspect-video rounded-lg"
                ></iframe>
              </div>
            </div>
          </div>
        )}

        {/* Music Videos Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {musicVideos.map((video) => (
            <div
              key={video.id}
              className="bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-all duration-300 hover:scale-105"
            >
              {/* Video Thumbnail */}
              <div className="relative aspect-video bg-gray-600 flex items-center justify-center">
                <div className="absolute inset-0 bg-black/20"></div>
                {video.thumbnail_url ? (
                  <img
                    src={getProxiedImageUrl(video.thumbnail_url)}
                    alt={video.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      // 如果图片加载失败，显示默认图标
                      const target = e.target as HTMLImageElement
                      target.style.display = 'none'
                      target.nextElementSibling?.classList.remove('hidden')
                    }}
                  />
                ) : null}
                <Video className={`text-gray-400 z-10 ${video.thumbnail_url ? 'hidden' : ''}`} size={48} />

                <button
                  onClick={() => handleVideoSelect(video.bilibili_embed_id)}
                  className="absolute inset-0 flex items-center justify-center bg-black/30 hover:bg-black/60 transition-all duration-200 z-20"
                >
                  <div className="w-16 h-16 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center shadow-lg">
                    <Play className="text-white ml-1" size={24} />
                  </div>
                </button>

                {/* External Link */}
                <div className="absolute top-4 right-4">
                  <a
                    href={video.bilibili_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-10 h-10 bg-black/70 backdrop-blur-sm hover:bg-red-600 rounded-full flex items-center justify-center transition-colors duration-200"
                    title="在B站观看"
                  >
                    <ExternalLink className="text-white" size={16} />
                  </a>
                </div>
              </div>

              {/* Video Info */}
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2 line-clamp-2">{video.title}</h3>
                <p className="text-gray-400 mb-2">演唱：{video.artist}</p>
                {video.description && (
                  <p className="text-gray-500 text-sm mb-4 line-clamp-3">{video.description}</p>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">
                    发布于 {new Date(video.created_at).toLocaleDateString('zh-CN')}
                  </span>
                  <a
                    href={video.bilibili_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-red-400 hover:text-red-300 text-sm font-semibold transition-colors duration-200"
                  >
                    B站观看 →
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {musicVideos.length === 0 && (
          <div className="text-center py-20">
            <Music className="mx-auto text-gray-600 mb-4" size={64} />
            <h3 className="text-2xl font-semibold text-gray-400 mb-2">
              No Music Videos Available
            </h3>
            <p className="text-gray-500">
              Stay tuned for more amazing music videos
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
